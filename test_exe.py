#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试打包后的可执行文件
"""

import subprocess
import time
import os
from pathlib import Path

def test_exe():
    """测试可执行文件"""
    exe_path = Path("dist/ProxyFetcher.exe")
    
    if not exe_path.exists():
        print("❌ 可执行文件不存在")
        return False
    
    print(f"✅ 找到可执行文件: {exe_path}")
    
    # 获取文件信息
    size_mb = exe_path.stat().st_size / (1024 * 1024)
    print(f"📏 文件大小: {size_mb:.1f} MB")
    
    # 尝试启动应用（非阻塞）
    print("🚀 启动应用程序...")
    try:
        process = subprocess.Popen([str(exe_path)], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # 等待几秒看是否正常启动
        time.sleep(3)
        
        # 检查进程状态
        poll = process.poll()
        if poll is None:
            print("✅ 应用程序正在运行")
            print("📱 请检查是否出现GUI窗口")
            print("⚠️ 如果看到GUI窗口，说明打包成功！")
            
            # 等待用户确认
            input("\n按Enter键终止测试进程...")
            
            # 终止进程
            process.terminate()
            try:
                process.wait(timeout=5)
                print("✅ 进程已正常终止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("⚠️ 进程被强制终止")
            
            return True
        else:
            print(f"❌ 应用程序启动失败，退出码: {poll}")
            stdout, stderr = process.communicate()
            if stderr:
                print(f"错误信息: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 可执行文件测试工具")
    print("=" * 40)
    
    if test_exe():
        print("\n🎉 测试完成！")
        print("如果您看到了GUI窗口，说明打包成功！")
        print("\n📋 功能测试清单:")
        print("□ GUI界面正常显示")
        print("□ 导航菜单可以切换")
        print("□ 主题切换功能正常")
        print("□ 参数配置可以修改")
        print("□ 代理获取功能正常")
        print("□ 日志显示正常")
    else:
        print("\n❌ 测试失败")
        print("请检查打包过程是否有错误")

if __name__ == "__main__":
    main()
