#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理节点获取器 - Windows 11 Fluent Design GUI版本
基于CustomTkinter实现现代化界面
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import queue
import os
import sys
from datetime import datetime
from typing import Optional, Tuple
import json

# 导入原始的ProxyFetcher类
from proxy_fetcher import ProxyFetcher

# 设置CustomTkinter主题
ctk.set_appearance_mode("system")  # 系统主题
ctk.set_default_color_theme("blue")  # 蓝色主题


class FluentColors:
    """Windows 11 Fluent Design 颜色规范"""

    # 主色调
    ACCENT_PRIMARY = "#0078D4"
    ACCENT_SECONDARY = "#106EBE"
    ACCENT_TERTIARY = "#005A9E"

    # 背景色
    BACKGROUND_PRIMARY = "#F3F3F3"
    BACKGROUND_SECONDARY = "#FAFAFA"
    BACKGROUND_TERTIARY = "#FFFFFF"

    # 文本色
    TEXT_PRIMARY = "#323130"
    TEXT_SECONDARY = "#605E5C"
    TEXT_TERTIARY = "#8A8886"

    # 状态色
    SUCCESS = "#107C10"
    WARNING = "#FF8C00"
    ERROR = "#D13438"
    INFO = "#0078D4"

    # 深色主题
    DARK_BACKGROUND_PRIMARY = "#202020"
    DARK_BACKGROUND_SECONDARY = "#2C2C2C"
    DARK_BACKGROUND_TERTIARY = "#383838"
    DARK_TEXT_PRIMARY = "#FFFFFF"
    DARK_TEXT_SECONDARY = "#E1E1E1"


class FluentProgressRing(ctk.CTkFrame):
    """Fluent Design 风格的环形进度条"""

    def __init__(self, parent, size=40, **kwargs):
        super().__init__(parent, **kwargs)
        self.size = size
        self.angle = 0
        self.is_running = False

        # 创建画布
        self.canvas = tk.Canvas(
            self,
            width=size,
            height=size,
            highlightthickness=0,
            bg=self.cget("fg_color")[1]
        )
        self.canvas.pack(padx=10, pady=10)

    def start(self):
        """开始动画"""
        self.is_running = True
        self._animate()

    def stop(self):
        """停止动画"""
        self.is_running = False
        self.canvas.delete("all")

    def _animate(self):
        """动画循环"""
        if not self.is_running:
            return

        self.canvas.delete("all")

        # 绘制环形进度
        center = self.size // 2
        radius = center - 5

        # 计算弧度
        start_angle = self.angle
        extent_angle = 90

        # 绘制弧形
        self.canvas.create_arc(
            center - radius, center - radius,
            center + radius, center + radius,
            start=start_angle, extent=extent_angle,
            outline=FluentColors.ACCENT_PRIMARY,
            width=3, style="arc"
        )

        # 更新角度
        self.angle = (self.angle + 10) % 360

        # 继续动画
        self.after(50, self._animate)


class ProxyFetcherGUI:
    """代理获取器GUI主类"""

    def __init__(self):
        self.root = ctk.CTk()
        self.setup_window()
        self.setup_variables()
        self.setup_ui()
        self.setup_queue()

        # 原始获取器实例
        self.fetcher = None
        self.fetch_thread = None

    def setup_window(self):
        """设置主窗口"""
        self.root.title("代理节点获取器 - Fluent Design")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)

        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        # 居中显示
        self.center_window()

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def setup_variables(self):
        """设置变量"""
        self.api_url = tk.StringVar(value="https://ohayoo-distribute.hf.space/api/v1/subscribe?token=335wsxrnx0ig4pg6&target=clash&list=1")
        self.fetch_count = tk.IntVar(value=3)
        self.thread_count = tk.IntVar(value=3)
        self.interval = tk.IntVar(value=1)
        self.output_dir = tk.StringVar(value=os.getcwd())

        # 状态变量
        self.is_fetching = tk.BooleanVar(value=False)
        self.current_progress = tk.StringVar(value="就绪")

    def setup_queue(self):
        """设置消息队列"""
        self.message_queue = queue.Queue()
        self.check_queue()

    def check_queue(self):
        """检查消息队列"""
        try:
            while True:
                message = self.message_queue.get_nowait()
                self.update_log(message)
        except queue.Empty:
            pass
        finally:
            self.root.after(100, self.check_queue)

    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        self.main_container = ctk.CTkFrame(self.root, corner_radius=0)
        self.main_container.pack(fill="both", expand=True, padx=0, pady=0)

        # 创建导航和内容区域
        self.create_navigation()
        self.create_content_area()

    def create_navigation(self):
        """创建导航栏"""
        # 左侧导航栏
        self.nav_frame = ctk.CTkFrame(
            self.main_container,
            width=200,
            corner_radius=0,
            fg_color=("gray90", "gray20")
        )
        self.nav_frame.pack(side="left", fill="y", padx=(0, 1))
        self.nav_frame.pack_propagate(False)

        # 应用标题
        title_label = ctk.CTkLabel(
            self.nav_frame,
            text="代理获取器",
            font=ctk.CTkFont(family="Segoe UI", size=20, weight="bold")
        )
        title_label.pack(pady=(20, 30), padx=20)

        # 导航按钮
        nav_buttons = [
            ("🏠 主页", self.show_home),
            ("⚙️ 设置", self.show_settings),
            ("📊 日志", self.show_logs),
            ("ℹ️ 关于", self.show_about)
        ]

        self.nav_button_frames = {}
        for text, command in nav_buttons:
            btn = ctk.CTkButton(
                self.nav_frame,
                text=text,
                command=command,
                height=40,
                corner_radius=8,
                font=ctk.CTkFont(family="Segoe UI", size=12),
                anchor="w"
            )
            btn.pack(pady=4, padx=20, fill="x")
            self.nav_button_frames[text] = btn

    def create_content_area(self):
        """创建内容区域"""
        # 右侧内容区域
        self.content_frame = ctk.CTkFrame(
            self.main_container,
            corner_radius=0,
            fg_color="transparent"
        )
        self.content_frame.pack(side="right", fill="both", expand=True)

        # 创建不同的页面
        self.create_home_page()
        self.create_settings_page()
        self.create_logs_page()
        self.create_about_page()

        # 默认显示主页
        self.show_home()

    def create_home_page(self):
        """创建主页"""
        self.home_page = ctk.CTkFrame(self.content_frame, fg_color="transparent")

        # 页面标题
        title = ctk.CTkLabel(
            self.home_page,
            text="代理节点获取",
            font=ctk.CTkFont(family="Segoe UI", size=24, weight="bold")
        )
        title.pack(pady=(20, 30), padx=20, anchor="w")

        # 配置区域
        config_frame = ctk.CTkFrame(self.home_page, corner_radius=12)
        config_frame.pack(pady=10, padx=20, fill="x")

        # 配置标题
        config_title = ctk.CTkLabel(
            config_frame,
            text="获取配置",
            font=ctk.CTkFont(family="Segoe UI", size=16, weight="bold")
        )
        config_title.pack(pady=(15, 10), padx=20, anchor="w")

        # 配置项
        self.create_config_inputs(config_frame)

        # 控制按钮区域
        self.create_control_buttons()

        # 进度显示区域
        self.create_progress_area()

    def create_config_inputs(self, parent):
        """创建配置输入控件"""
        # 获取次数
        count_frame = ctk.CTkFrame(parent, fg_color="transparent")
        count_frame.pack(pady=5, padx=20, fill="x")

        ctk.CTkLabel(
            count_frame,
            text="获取次数:",
            font=ctk.CTkFont(family="Segoe UI", size=12)
        ).pack(side="left", padx=(0, 10))

        self.count_spinbox = ctk.CTkEntry(
            count_frame,
            textvariable=self.fetch_count,
            width=100,
            height=32,
            corner_radius=8,
            font=ctk.CTkFont(family="Segoe UI", size=12)
        )
        self.count_spinbox.pack(side="left")

        # 线程数
        thread_frame = ctk.CTkFrame(parent, fg_color="transparent")
        thread_frame.pack(pady=5, padx=20, fill="x")

        ctk.CTkLabel(
            thread_frame,
            text="线程数:",
            font=ctk.CTkFont(family="Segoe UI", size=12)
        ).pack(side="left", padx=(0, 10))

        self.thread_spinbox = ctk.CTkEntry(
            thread_frame,
            textvariable=self.thread_count,
            width=100,
            height=32,
            corner_radius=8,
            font=ctk.CTkFont(family="Segoe UI", size=12)
        )
        self.thread_spinbox.pack(side="left")

        # 间隔时间
        interval_frame = ctk.CTkFrame(parent, fg_color="transparent")
        interval_frame.pack(pady=5, padx=20, fill="x")

        ctk.CTkLabel(
            interval_frame,
            text="间隔时间(秒):",
            font=ctk.CTkFont(family="Segoe UI", size=12)
        ).pack(side="left", padx=(0, 10))

        self.interval_spinbox = ctk.CTkEntry(
            interval_frame,
            textvariable=self.interval,
            width=100,
            height=32,
            corner_radius=8,
            font=ctk.CTkFont(family="Segoe UI", size=12)
        )
        self.interval_spinbox.pack(side="left")

        # 输出目录
        output_frame = ctk.CTkFrame(parent, fg_color="transparent")
        output_frame.pack(pady=5, padx=20, fill="x")

        ctk.CTkLabel(
            output_frame,
            text="输出目录:",
            font=ctk.CTkFont(family="Segoe UI", size=12)
        ).pack(side="left", padx=(0, 10))

        self.output_entry = ctk.CTkEntry(
            output_frame,
            textvariable=self.output_dir,
            height=32,
            corner_radius=8,
            font=ctk.CTkFont(family="Segoe UI", size=12)
        )
        self.output_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))

        self.browse_btn = ctk.CTkButton(
            output_frame,
            text="浏览",
            command=self.browse_output_dir,
            width=80,
            height=32,
            corner_radius=8,
            font=ctk.CTkFont(family="Segoe UI", size=12)
        )
        self.browse_btn.pack(side="right")

    def create_control_buttons(self):
        """创建控制按钮"""
        button_frame = ctk.CTkFrame(self.home_page, corner_radius=12)
        button_frame.pack(pady=20, padx=20, fill="x")

        # 按钮容器
        btn_container = ctk.CTkFrame(button_frame, fg_color="transparent")
        btn_container.pack(pady=20, padx=20)

        # 开始按钮
        self.start_btn = ctk.CTkButton(
            btn_container,
            text="🚀 开始获取",
            command=self.start_fetch,
            width=120,
            height=40,
            corner_radius=8,
            font=ctk.CTkFont(family="Segoe UI", size=14, weight="bold"),
            fg_color=FluentColors.ACCENT_PRIMARY,
            hover_color=FluentColors.ACCENT_SECONDARY
        )
        self.start_btn.pack(side="left", padx=(0, 10))

        # 停止按钮
        self.stop_btn = ctk.CTkButton(
            btn_container,
            text="⏹️ 停止",
            command=self.stop_fetch,
            width=120,
            height=40,
            corner_radius=8,
            font=ctk.CTkFont(family="Segoe UI", size=14),
            fg_color=FluentColors.ERROR,
            hover_color="#B71C1C",
            state="disabled"
        )
        self.stop_btn.pack(side="left", padx=10)

        # 清空日志按钮
        self.clear_btn = ctk.CTkButton(
            btn_container,
            text="🗑️ 清空日志",
            command=self.clear_logs,
            width=120,
            height=40,
            corner_radius=8,
            font=ctk.CTkFont(family="Segoe UI", size=14),
            fg_color="gray",
            hover_color="gray30"
        )
        self.clear_btn.pack(side="left", padx=10)

    def create_progress_area(self):
        """创建进度显示区域"""
        progress_frame = ctk.CTkFrame(self.home_page, corner_radius=12)
        progress_frame.pack(pady=10, padx=20, fill="both", expand=True)

        # 进度标题
        progress_title = ctk.CTkLabel(
            progress_frame,
            text="执行状态",
            font=ctk.CTkFont(family="Segoe UI", size=16, weight="bold")
        )
        progress_title.pack(pady=(15, 10), padx=20, anchor="w")

        # 状态显示
        status_frame = ctk.CTkFrame(progress_frame, fg_color="transparent")
        status_frame.pack(pady=5, padx=20, fill="x")

        self.status_label = ctk.CTkLabel(
            status_frame,
            textvariable=self.current_progress,
            font=ctk.CTkFont(family="Segoe UI", size=12)
        )
        self.status_label.pack(side="left")

        # 进度环
        self.progress_ring = FluentProgressRing(status_frame, size=30)
        self.progress_ring.pack(side="right", padx=10)

        # 简化的日志显示
        log_frame = ctk.CTkFrame(progress_frame, fg_color="transparent")
        log_frame.pack(pady=10, padx=20, fill="both", expand=True)

        self.log_text = ctk.CTkTextbox(
            log_frame,
            height=200,
            corner_radius=8,
            font=ctk.CTkFont(family="Consolas", size=11),
            wrap="word"
        )
        self.log_text.pack(fill="both", expand=True)

    def create_settings_page(self):
        """创建设置页面"""
        self.settings_page = ctk.CTkFrame(self.content_frame, fg_color="transparent")

        # 页面标题
        title = ctk.CTkLabel(
            self.settings_page,
            text="应用设置",
            font=ctk.CTkFont(family="Segoe UI", size=24, weight="bold")
        )
        title.pack(pady=(20, 30), padx=20, anchor="w")

        # API设置
        api_frame = ctk.CTkFrame(self.settings_page, corner_radius=12)
        api_frame.pack(pady=10, padx=20, fill="x")

        api_title = ctk.CTkLabel(
            api_frame,
            text="API 配置",
            font=ctk.CTkFont(family="Segoe UI", size=16, weight="bold")
        )
        api_title.pack(pady=(15, 10), padx=20, anchor="w")

        # API URL输入
        url_frame = ctk.CTkFrame(api_frame, fg_color="transparent")
        url_frame.pack(pady=10, padx=20, fill="x")

        ctk.CTkLabel(
            url_frame,
            text="API地址:",
            font=ctk.CTkFont(family="Segoe UI", size=12)
        ).pack(anchor="w", pady=(0, 5))

        self.api_entry = ctk.CTkEntry(
            url_frame,
            textvariable=self.api_url,
            height=32,
            corner_radius=8,
            font=ctk.CTkFont(family="Segoe UI", size=11)
        )
        self.api_entry.pack(fill="x", pady=(0, 15))

        # 主题设置
        theme_frame = ctk.CTkFrame(self.settings_page, corner_radius=12)
        theme_frame.pack(pady=10, padx=20, fill="x")

        theme_title = ctk.CTkLabel(
            theme_frame,
            text="外观设置",
            font=ctk.CTkFont(family="Segoe UI", size=16, weight="bold")
        )
        theme_title.pack(pady=(15, 10), padx=20, anchor="w")

        # 主题选择
        theme_option_frame = ctk.CTkFrame(theme_frame, fg_color="transparent")
        theme_option_frame.pack(pady=10, padx=20, fill="x")

        ctk.CTkLabel(
            theme_option_frame,
            text="主题模式:",
            font=ctk.CTkFont(family="Segoe UI", size=12)
        ).pack(side="left", padx=(0, 10))

        self.theme_var = tk.StringVar(value="system")
        theme_menu = ctk.CTkOptionMenu(
            theme_option_frame,
            variable=self.theme_var,
            values=["light", "dark", "system"],
            command=self.change_theme,
            width=120,
            height=32,
            corner_radius=8,
            font=ctk.CTkFont(family="Segoe UI", size=12)
        )
        theme_menu.pack(side="left")

        # 保存设置按钮
        save_frame = ctk.CTkFrame(theme_frame, fg_color="transparent")
        save_frame.pack(pady=15, padx=20, fill="x")

        save_btn = ctk.CTkButton(
            save_frame,
            text="💾 保存设置",
            command=self.save_settings,
            width=120,
            height=32,
            corner_radius=8,
            font=ctk.CTkFont(family="Segoe UI", size=12),
            fg_color=FluentColors.ACCENT_PRIMARY,
            hover_color=FluentColors.ACCENT_SECONDARY
        )
        save_btn.pack(anchor="w")

    def create_logs_page(self):
        """创建日志页面"""
        self.logs_page = ctk.CTkFrame(self.content_frame, fg_color="transparent")

        # 页面标题
        title = ctk.CTkLabel(
            self.logs_page,
            text="执行日志",
            font=ctk.CTkFont(family="Segoe UI", size=24, weight="bold")
        )
        title.pack(pady=(20, 30), padx=20, anchor="w")

        # 日志控制区域
        control_frame = ctk.CTkFrame(self.logs_page, corner_radius=12)
        control_frame.pack(pady=10, padx=20, fill="x")

        control_container = ctk.CTkFrame(control_frame, fg_color="transparent")
        control_container.pack(pady=15, padx=20, fill="x")

        # 清空按钮
        clear_log_btn = ctk.CTkButton(
            control_container,
            text="🗑️ 清空日志",
            command=self.clear_logs,
            width=120,
            height=32,
            corner_radius=8,
            font=ctk.CTkFont(family="Segoe UI", size=12),
            fg_color="gray",
            hover_color="gray30"
        )
        clear_log_btn.pack(side="left", padx=(0, 10))

        # 导出按钮
        export_btn = ctk.CTkButton(
            control_container,
            text="📤 导出日志",
            command=self.export_logs,
            width=120,
            height=32,
            corner_radius=8,
            font=ctk.CTkFont(family="Segoe UI", size=12),
            fg_color=FluentColors.ACCENT_PRIMARY,
            hover_color=FluentColors.ACCENT_SECONDARY
        )
        export_btn.pack(side="left")

        # 详细日志显示
        log_frame = ctk.CTkFrame(self.logs_page, corner_radius=12)
        log_frame.pack(pady=10, padx=20, fill="both", expand=True)

        log_title = ctk.CTkLabel(
            log_frame,
            text="详细日志",
            font=ctk.CTkFont(family="Segoe UI", size=16, weight="bold")
        )
        log_title.pack(pady=(15, 10), padx=20, anchor="w")

        # 创建带滚动条的文本框
        text_frame = ctk.CTkFrame(log_frame, fg_color="transparent")
        text_frame.pack(pady=10, padx=20, fill="both", expand=True)

        self.detailed_log_text = ctk.CTkTextbox(
            text_frame,
            corner_radius=8,
            font=ctk.CTkFont(family="Consolas", size=11),
            wrap="word"
        )
        self.detailed_log_text.pack(fill="both", expand=True)

    def create_about_page(self):
        """创建关于页面"""
        self.about_page = ctk.CTkFrame(self.content_frame, fg_color="transparent")

        # 页面标题
        title = ctk.CTkLabel(
            self.about_page,
            text="关于应用",
            font=ctk.CTkFont(family="Segoe UI", size=24, weight="bold")
        )
        title.pack(pady=(20, 30), padx=20, anchor="w")

        # 应用信息
        info_frame = ctk.CTkFrame(self.about_page, corner_radius=12)
        info_frame.pack(pady=10, padx=20, fill="x")

        # 应用图标和名称
        header_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
        header_frame.pack(pady=20, padx=20, fill="x")

        app_name = ctk.CTkLabel(
            header_frame,
            text="🚀 代理节点获取器",
            font=ctk.CTkFont(family="Segoe UI", size=20, weight="bold")
        )
        app_name.pack(anchor="w")

        version_label = ctk.CTkLabel(
            header_frame,
            text="版本 1.0.0 - Fluent Design Edition",
            font=ctk.CTkFont(family="Segoe UI", size=12),
            text_color="gray"
        )
        version_label.pack(anchor="w", pady=(5, 0))

        # 功能介绍
        features_text = """
✨ 主要功能：
• 多线程并发获取代理节点
• 支持自定义获取次数和线程数
• 自动保存为YAML格式文件
• 现代化Fluent Design界面
• 支持浅色/深色主题切换
• 实时日志显示和导出

🛠️ 技术栈：
• Python 3.8+
• CustomTkinter (现代GUI框架)
• Requests (HTTP请求)
• Threading (多线程支持)

📧 联系方式：
如有问题或建议，请联系开发者
        """

        features_label = ctk.CTkLabel(
            info_frame,
            text=features_text,
            font=ctk.CTkFont(family="Segoe UI", size=11),
            justify="left"
        )
        features_label.pack(pady=10, padx=20, anchor="w")

    # 页面切换方法
    def show_home(self):
        """显示主页"""
        self.hide_all_pages()
        self.home_page.pack(fill="both", expand=True)

    def show_settings(self):
        """显示设置页面"""
        self.hide_all_pages()
        self.settings_page.pack(fill="both", expand=True)

    def show_logs(self):
        """显示日志页面"""
        self.hide_all_pages()
        self.logs_page.pack(fill="both", expand=True)

    def show_about(self):
        """显示关于页面"""
        self.hide_all_pages()
        self.about_page.pack(fill="both", expand=True)

    def hide_all_pages(self):
        """隐藏所有页面"""
        for page in [self.home_page, self.settings_page, self.logs_page, self.about_page]:
            page.pack_forget()

    # 事件处理方法
    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(
            title="选择输出目录",
            initialdir=self.output_dir.get()
        )
        if directory:
            self.output_dir.set(directory)

    def change_theme(self, theme):
        """更改主题"""
        ctk.set_appearance_mode(theme)

    def save_settings(self):
        """保存设置"""
        settings = {
            "api_url": self.api_url.get(),
            "theme": self.theme_var.get(),
            "output_dir": self.output_dir.get()
        }

        try:
            with open("settings.json", "w", encoding="utf-8") as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            messagebox.showinfo("成功", "设置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败: {e}")

    def load_settings(self):
        """加载设置"""
        try:
            if os.path.exists("settings.json"):
                with open("settings.json", "r", encoding="utf-8") as f:
                    settings = json.load(f)

                self.api_url.set(settings.get("api_url", self.api_url.get()))
                self.theme_var.set(settings.get("theme", "system"))
                self.output_dir.set(settings.get("output_dir", os.getcwd()))

                # 应用主题
                ctk.set_appearance_mode(self.theme_var.get())
        except Exception as e:
            print(f"加载设置失败: {e}")

    def clear_logs(self):
        """清空日志"""
        self.log_text.delete("1.0", "end")
        if hasattr(self, 'detailed_log_text'):
            self.detailed_log_text.delete("1.0", "end")

    def export_logs(self):
        """导出日志"""
        try:
            filename = filedialog.asksaveasfilename(
                title="导出日志",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if filename:
                log_content = self.detailed_log_text.get("1.0", "end-1c")
                with open(filename, "w", encoding="utf-8") as f:
                    f.write(log_content)
                messagebox.showinfo("成功", f"日志已导出到: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"导出日志失败: {e}")

    def update_log(self, message):
        """更新日志显示"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        # 更新主页日志
        self.log_text.insert("end", formatted_message)
        self.log_text.see("end")

        # 更新详细日志页面
        if hasattr(self, 'detailed_log_text'):
            self.detailed_log_text.insert("end", formatted_message)
            self.detailed_log_text.see("end")

    def start_fetch(self):
        """开始获取"""
        try:
            # 验证输入
            fetch_count = self.fetch_count.get()
            thread_count = self.thread_count.get()
            interval = self.interval.get()

            if fetch_count <= 0:
                messagebox.showerror("错误", "获取次数必须大于0")
                return

            if thread_count <= 0:
                messagebox.showerror("错误", "线程数必须大于0")
                return

            if thread_count > fetch_count:
                thread_count = fetch_count
                self.thread_count.set(thread_count)

            if interval < 0:
                messagebox.showerror("错误", "间隔时间不能为负数")
                return

            # 检查输出目录
            output_dir = self.output_dir.get()
            if not os.path.exists(output_dir):
                try:
                    os.makedirs(output_dir)
                except Exception as e:
                    messagebox.showerror("错误", f"创建输出目录失败: {e}")
                    return

            # 切换到输出目录
            original_dir = os.getcwd()
            os.chdir(output_dir)

            # 更新UI状态
            self.is_fetching.set(True)
            self.start_btn.configure(state="disabled")
            self.stop_btn.configure(state="normal")
            self.current_progress.set("正在获取...")
            self.progress_ring.start()

            # 清空日志
            self.clear_logs()

            # 创建获取器实例
            self.fetcher = ProxyFetcher(self.api_url.get())

            # 启动获取线程
            self.fetch_thread = threading.Thread(
                target=self._fetch_worker,
                args=(fetch_count, thread_count, interval, original_dir),
                daemon=True
            )
            self.fetch_thread.start()

        except Exception as e:
            messagebox.showerror("错误", f"启动获取失败: {e}")
            self.stop_fetch()

    def _fetch_worker(self, fetch_count, thread_count, interval, original_dir):
        """获取工作线程"""
        try:
            # 重写ProxyFetcher的safe_print方法，使其输出到GUI
            def gui_safe_print(message):
                self.message_queue.put(message)

            self.fetcher.safe_print = gui_safe_print

            # 执行获取
            self.fetcher.run_fetch_cycle(fetch_count, thread_count, interval)

            # 完成后更新状态
            self.message_queue.put("✅ 获取完成！")

        except Exception as e:
            self.message_queue.put(f"❌ 获取失败: {e}")
        finally:
            # 恢复原始目录
            os.chdir(original_dir)

            # 更新UI状态
            self.root.after(0, self._fetch_completed)

    def _fetch_completed(self):
        """获取完成后的UI更新"""
        self.is_fetching.set(False)
        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")
        self.current_progress.set("获取完成")
        self.progress_ring.stop()

    def stop_fetch(self):
        """停止获取"""
        self.is_fetching.set(False)
        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")
        self.current_progress.set("已停止")
        self.progress_ring.stop()

        if self.fetch_thread and self.fetch_thread.is_alive():
            # 注意：这里只是更新UI状态，实际的线程停止需要在ProxyFetcher中实现
            self.update_log("⚠️ 正在尝试停止获取...")

    def run(self):
        """运行应用"""
        # 加载设置
        self.load_settings()

        # 启动主循环
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = ProxyFetcherGUI()
        app.run()
    except Exception as e:
        print(f"应用启动失败: {e}")
        messagebox.showerror("错误", f"应用启动失败: {e}")


if __name__ == "__main__":
    main()
