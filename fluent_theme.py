#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows 11 Fluent Design 主题配置
提供完整的颜色方案和样式定义
"""

import customtkinter as ctk
from typing import Dict, Any

class FluentTheme:
    """Fluent Design 主题管理器"""
    
    # Windows 11 官方颜色规范
    COLORS = {
        # 主色调 (Accent Colors)
        "accent_primary": "#0078D4",
        "accent_secondary": "#106EBE", 
        "accent_tertiary": "#005A9E",
        "accent_quaternary": "#004578",
        
        # 浅色主题背景
        "light_bg_primary": "#F3F3F3",
        "light_bg_secondary": "#FAFAFA",
        "light_bg_tertiary": "#FFFFFF",
        "light_bg_quaternary": "#F9F9F9",
        
        # 浅色主题文本
        "light_text_primary": "#323130",
        "light_text_secondary": "#605E5C",
        "light_text_tertiary": "#8A8886",
        "light_text_disabled": "#C8C6C4",
        
        # 深色主题背景
        "dark_bg_primary": "#202020",
        "dark_bg_secondary": "#2C2C2C",
        "dark_bg_tertiary": "#383838",
        "dark_bg_quaternary": "#404040",
        
        # 深色主题文本
        "dark_text_primary": "#FFFFFF",
        "dark_text_secondary": "#E1E1E1",
        "dark_text_tertiary": "#C8C8C8",
        "dark_text_disabled": "#6D6D6D",
        
        # 状态颜色
        "success": "#107C10",
        "warning": "#FF8C00",
        "error": "#D13438",
        "info": "#0078D4",
        
        # 边框和分割线
        "light_border": "#E1DFDD",
        "dark_border": "#484644",
        
        # 悬停效果
        "light_hover": "#F3F2F1",
        "dark_hover": "#484644",
        
        # 选中效果
        "light_selected": "#DEECF9",
        "dark_selected": "#2B2B2B",
    }
    
    # 字体规范
    FONTS = {
        "family": "Segoe UI",
        "size_small": 10,
        "size_normal": 12,
        "size_medium": 14,
        "size_large": 16,
        "size_xlarge": 20,
        "size_xxlarge": 24,
        "size_title": 28,
    }
    
    # 间距规范 (8px增量)
    SPACING = {
        "xs": 4,
        "sm": 8,
        "md": 16,
        "lg": 24,
        "xl": 32,
        "xxl": 40,
        "xxxl": 48,
    }
    
    # 圆角规范
    RADIUS = {
        "small": 4,
        "medium": 8,
        "large": 12,
        "xlarge": 16,
    }
    
    # 阴影规范
    SHADOWS = {
        "elevation_2": "0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)",
        "elevation_4": "0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)",
        "elevation_8": "0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23)",
        "elevation_16": "0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22)",
        "elevation_24": "0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22)",
    }
    
    @classmethod
    def get_theme_config(cls, mode: str = "system") -> Dict[str, Any]:
        """
        获取主题配置
        
        Args:
            mode: 主题模式 ("light", "dark", "system")
            
        Returns:
            主题配置字典
        """
        if mode == "light":
            return cls._get_light_theme()
        elif mode == "dark":
            return cls._get_dark_theme()
        else:  # system
            return cls._get_system_theme()
    
    @classmethod
    def _get_light_theme(cls) -> Dict[str, Any]:
        """获取浅色主题配置"""
        return {
            "CTk": {
                "fg_color": [cls.COLORS["light_bg_primary"], cls.COLORS["light_bg_primary"]]
            },
            "CTkToplevel": {
                "fg_color": [cls.COLORS["light_bg_primary"], cls.COLORS["light_bg_primary"]]
            },
            "CTkFrame": {
                "corner_radius": cls.RADIUS["medium"],
                "border_width": 0,
                "fg_color": [cls.COLORS["light_bg_tertiary"], cls.COLORS["light_bg_tertiary"]],
                "top_fg_color": [cls.COLORS["light_bg_secondary"], cls.COLORS["light_bg_secondary"]],
                "border_color": [cls.COLORS["light_border"], cls.COLORS["light_border"]]
            },
            "CTkButton": {
                "corner_radius": cls.RADIUS["small"],
                "border_width": 0,
                "fg_color": [cls.COLORS["accent_primary"], cls.COLORS["accent_primary"]],
                "hover_color": [cls.COLORS["accent_secondary"], cls.COLORS["accent_secondary"]],
                "border_color": [cls.COLORS["accent_primary"], cls.COLORS["accent_primary"]],
                "text_color": ["white", "white"],
                "text_color_disabled": [cls.COLORS["light_text_disabled"], cls.COLORS["light_text_disabled"]]
            },
            "CTkLabel": {
                "corner_radius": 0,
                "fg_color": "transparent",
                "text_color": [cls.COLORS["light_text_primary"], cls.COLORS["light_text_primary"]]
            },
            "CTkEntry": {
                "corner_radius": cls.RADIUS["medium"],
                "border_width": 1,
                "fg_color": [cls.COLORS["light_bg_tertiary"], cls.COLORS["light_bg_tertiary"]],
                "border_color": [cls.COLORS["light_border"], cls.COLORS["light_border"]],
                "text_color": [cls.COLORS["light_text_primary"], cls.COLORS["light_text_primary"]],
                "placeholder_text_color": [cls.COLORS["light_text_tertiary"], cls.COLORS["light_text_tertiary"]]
            },
            "CTkTextbox": {
                "corner_radius": cls.RADIUS["medium"],
                "border_width": 1,
                "fg_color": [cls.COLORS["light_bg_tertiary"], cls.COLORS["light_bg_tertiary"]],
                "border_color": [cls.COLORS["light_border"], cls.COLORS["light_border"]],
                "text_color": [cls.COLORS["light_text_primary"], cls.COLORS["light_text_primary"]],
                "scrollbar_button_color": [cls.COLORS["light_text_tertiary"], cls.COLORS["light_text_tertiary"]],
                "scrollbar_button_hover_color": [cls.COLORS["light_text_secondary"], cls.COLORS["light_text_secondary"]]
            },
            "CTkScrollbar": {
                "corner_radius": cls.RADIUS["large"],
                "border_spacing": 4,
                "fg_color": "transparent",
                "button_color": [cls.COLORS["light_text_tertiary"], cls.COLORS["light_text_tertiary"]],
                "button_hover_color": [cls.COLORS["light_text_secondary"], cls.COLORS["light_text_secondary"]]
            },
            "CTkCheckBox": {
                "corner_radius": cls.RADIUS["small"],
                "border_width": 2,
                "fg_color": [cls.COLORS["accent_primary"], cls.COLORS["accent_primary"]],
                "border_color": [cls.COLORS["light_border"], cls.COLORS["light_border"]],
                "hover_color": [cls.COLORS["accent_secondary"], cls.COLORS["accent_secondary"]],
                "checkmark_color": ["white", "white"],
                "text_color": [cls.COLORS["light_text_primary"], cls.COLORS["light_text_primary"]],
                "text_color_disabled": [cls.COLORS["light_text_disabled"], cls.COLORS["light_text_disabled"]]
            },
            "CTkSwitch": {
                "corner_radius": 1000,
                "border_width": 3,
                "button_length": 0,
                "fg_color": [cls.COLORS["light_border"], cls.COLORS["light_border"]],
                "progress_color": [cls.COLORS["accent_primary"], cls.COLORS["accent_primary"]],
                "button_color": ["white", "white"],
                "button_hover_color": [cls.COLORS["light_hover"], cls.COLORS["light_hover"]],
                "text_color": [cls.COLORS["light_text_primary"], cls.COLORS["light_text_primary"]],
                "text_color_disabled": [cls.COLORS["light_text_disabled"], cls.COLORS["light_text_disabled"]]
            },
            "CTkRadioButton": {
                "corner_radius": 1000,
                "border_width_checked": 6,
                "border_width_unchecked": 3,
                "fg_color": [cls.COLORS["accent_primary"], cls.COLORS["accent_primary"]],
                "border_color": [cls.COLORS["light_border"], cls.COLORS["light_border"]],
                "hover_color": [cls.COLORS["accent_secondary"], cls.COLORS["accent_secondary"]],
                "text_color": [cls.COLORS["light_text_primary"], cls.COLORS["light_text_primary"]],
                "text_color_disabled": [cls.COLORS["light_text_disabled"], cls.COLORS["light_text_disabled"]]
            },
            "CTkProgressBar": {
                "corner_radius": 1000,
                "border_width": 0,
                "fg_color": [cls.COLORS["light_border"], cls.COLORS["light_border"]],
                "progress_color": [cls.COLORS["accent_primary"], cls.COLORS["accent_primary"]],
                "border_color": [cls.COLORS["light_border"], cls.COLORS["light_border"]]
            },
            "CTkSlider": {
                "corner_radius": 1000,
                "button_corner_radius": 1000,
                "border_width": 6,
                "button_length": 0,
                "fg_color": [cls.COLORS["light_border"], cls.COLORS["light_border"]],
                "progress_color": [cls.COLORS["accent_primary"], cls.COLORS["accent_primary"]],
                "button_color": ["white", "white"],
                "button_hover_color": [cls.COLORS["light_hover"], cls.COLORS["light_hover"]]
            },
            "CTkOptionMenu": {
                "corner_radius": cls.RADIUS["medium"],
                "fg_color": [cls.COLORS["light_bg_tertiary"], cls.COLORS["light_bg_tertiary"]],
                "button_color": [cls.COLORS["light_border"], cls.COLORS["light_border"]],
                "button_hover_color": [cls.COLORS["light_hover"], cls.COLORS["light_hover"]],
                "text_color": [cls.COLORS["light_text_primary"], cls.COLORS["light_text_primary"]],
                "text_color_disabled": [cls.COLORS["light_text_disabled"], cls.COLORS["light_text_disabled"]]
            },
            "CTkComboBox": {
                "corner_radius": cls.RADIUS["medium"],
                "border_width": 1,
                "fg_color": [cls.COLORS["light_bg_tertiary"], cls.COLORS["light_bg_tertiary"]],
                "border_color": [cls.COLORS["light_border"], cls.COLORS["light_border"]],
                "button_color": [cls.COLORS["light_border"], cls.COLORS["light_border"]],
                "button_hover_color": [cls.COLORS["light_hover"], cls.COLORS["light_hover"]],
                "text_color": [cls.COLORS["light_text_primary"], cls.COLORS["light_text_primary"]],
                "text_color_disabled": [cls.COLORS["light_text_disabled"], cls.COLORS["light_text_disabled"]]
            },
            "CTkScrollableFrame": {
                "label_fg_color": [cls.COLORS["light_bg_primary"], cls.COLORS["light_bg_primary"]]
            },
            "CTkSegmentedButton": {
                "corner_radius": cls.RADIUS["medium"],
                "border_width": 0,
                "fg_color": [cls.COLORS["light_bg_secondary"], cls.COLORS["light_bg_secondary"]],
                "selected_color": [cls.COLORS["accent_primary"], cls.COLORS["accent_primary"]],
                "selected_hover_color": [cls.COLORS["accent_secondary"], cls.COLORS["accent_secondary"]],
                "unselected_color": [cls.COLORS["light_bg_secondary"], cls.COLORS["light_bg_secondary"]],
                "unselected_hover_color": [cls.COLORS["light_hover"], cls.COLORS["light_hover"]],
                "text_color": [cls.COLORS["light_text_primary"], cls.COLORS["light_text_primary"]],
                "text_color_disabled": [cls.COLORS["light_text_disabled"], cls.COLORS["light_text_disabled"]]
            },
            "CTkTabview": {
                "corner_radius": cls.RADIUS["medium"],
                "border_width": 0,
                "fg_color": [cls.COLORS["light_bg_secondary"], cls.COLORS["light_bg_secondary"]],
                "segmented_button_fg_color": [cls.COLORS["light_bg_tertiary"], cls.COLORS["light_bg_tertiary"]],
                "segmented_button_selected_color": [cls.COLORS["accent_primary"], cls.COLORS["accent_primary"]],
                "segmented_button_selected_hover_color": [cls.COLORS["accent_secondary"], cls.COLORS["accent_secondary"]],
                "segmented_button_unselected_color": [cls.COLORS["light_bg_tertiary"], cls.COLORS["light_bg_tertiary"]],
                "segmented_button_unselected_hover_color": [cls.COLORS["light_hover"], cls.COLORS["light_hover"]],
                "text_color": [cls.COLORS["light_text_primary"], cls.COLORS["light_text_primary"]],
                "text_color_disabled": [cls.COLORS["light_text_disabled"], cls.COLORS["light_text_disabled"]]
            }
        }
    
    @classmethod
    def _get_dark_theme(cls) -> Dict[str, Any]:
        """获取深色主题配置"""
        # 深色主题配置（类似浅色主题但使用深色颜色）
        # 这里简化处理，实际应用中需要完整定义
        return {
            "CTk": {
                "fg_color": [cls.COLORS["dark_bg_primary"], cls.COLORS["dark_bg_primary"]]
            },
            # ... 其他控件的深色主题配置
        }
    
    @classmethod
    def _get_system_theme(cls) -> Dict[str, Any]:
        """获取系统主题配置"""
        # 系统主题会根据系统设置自动切换
        return cls._get_light_theme()  # 默认使用浅色主题
    
    @classmethod
    def apply_theme(cls, mode: str = "system"):
        """
        应用主题
        
        Args:
            mode: 主题模式
        """
        theme_config = cls.get_theme_config(mode)
        
        # 设置CustomTkinter主题
        ctk.set_appearance_mode(mode)
        
        # 如果需要自定义主题，可以在这里设置
        # ctk.set_default_color_theme(theme_config)
    
    @classmethod
    def get_font(cls, size: str = "normal", weight: str = "normal") -> ctk.CTkFont:
        """
        获取字体
        
        Args:
            size: 字体大小 ("small", "normal", "medium", "large", etc.)
            weight: 字体粗细 ("normal", "bold")
            
        Returns:
            CTkFont对象
        """
        font_size = cls.FONTS.get(f"size_{size}", cls.FONTS["size_normal"])
        return ctk.CTkFont(
            family=cls.FONTS["family"],
            size=font_size,
            weight=weight
        )
    
    @classmethod
    def get_color(cls, color_name: str, theme_mode: str = "light") -> str:
        """
        获取颜色值
        
        Args:
            color_name: 颜色名称
            theme_mode: 主题模式
            
        Returns:
            颜色值
        """
        if theme_mode == "dark" and f"dark_{color_name}" in cls.COLORS:
            return cls.COLORS[f"dark_{color_name}"]
        elif theme_mode == "light" and f"light_{color_name}" in cls.COLORS:
            return cls.COLORS[f"light_{color_name}"]
        else:
            return cls.COLORS.get(color_name, "#000000")
