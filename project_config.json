{"project": {"name": "代理节点获取器", "version": "1.0.0", "description": "基于Windows 11 Fluent Design的现代化代理节点获取工具", "author": "Fluent Design Studio", "license": "MIT", "python_version": ">=3.8"}, "gui": {"framework": "CustomTkinter", "theme": "Windows 11 Fluent Design", "font_family": "Segoe UI", "default_size": "900x700", "min_size": "800x600", "features": ["NavigationView布局", "浅色/深色主题切换", "Mica/Acrylic背景效果", "Fluent风格按钮", "环形进度指示器", "实时日志显示"]}, "design_specs": {"colors": {"accent_primary": "#0078D4", "accent_secondary": "#106EBE", "success": "#107C10", "warning": "#FF8C00", "error": "#D13438"}, "spacing": {"unit": "8px", "increments": [8, 16, 24, 32, 40, 48]}, "radius": {"small": "4px", "medium": "8px", "large": "12px", "xlarge": "16px"}, "fonts": {"small": "10px", "normal": "12px", "medium": "14px", "large": "16px", "title": "20px+"}}, "dependencies": {"runtime": ["customtkinter>=5.2.0", "requests>=2.31.0", "Pillow>=10.0.0", "packaging>=23.0"], "development": ["pyinstaller>=5.0", "pytest>=7.0", "black>=23.0", "flake8>=6.0"]}, "build": {"output_name": "ProxyFetcher", "icon": "icon.ico", "console": false, "onefile": true, "uac_admin": true, "optimization": 2, "hidden_imports": ["customtkinter", "PIL", "PIL._tkinter_finder"]}, "features": {"core": ["多线程并发获取", "自定义配置参数", "YAML格式保存", "实时状态显示", "异常处理机制"], "ui": ["现代化界面设计", "响应式布局", "主题切换", "设置持久化", "日志导出"], "advanced": ["线程安全机制", "消息队列通信", "资源自动管理", "错误恢复机制"]}, "file_structure": {"main_files": ["proxy_fetcher.py", "proxy_fetcher_gui.py", "fluent_theme.py", "run.py"], "config_files": ["requirements.txt", "build_config.py", "project_config.json"], "scripts": ["启动应用.bat", "test_app.py"], "docs": ["README.md"], "output": ["dist/", "build/", "settings.json"]}, "usage": {"quick_start": "python run.py", "manual_start": "python proxy_fetcher_gui.py", "build": "python build_config.py", "test": "python test_app.py"}, "compatibility": {"os": ["Windows 10", "Windows 11"], "python": ["3.8", "3.9", "3.10", "3.11", "3.12"], "frameworks": ["CustomTkinter 5.2+"]}}