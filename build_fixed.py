#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复编码问题的打包脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    """主函数"""
    print("🚀 代理节点获取器 - 修复版打包工具")
    print("=" * 50)
    
    # 检查文件
    if not Path("proxy_fetcher_gui.py").exists():
        print("❌ 缺少 proxy_fetcher_gui.py 文件")
        return
    
    if not Path("proxy_fetcher.py").exists():
        print("❌ 缺少 proxy_fetcher.py 文件")
        return
    
    print("✅ 文件检查完成")
    
    # 构建命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--name=ProxyFetcher_v2",
        "--onefile",
        "--windowed",
        "--add-data=proxy_fetcher.py;.",
        "--hidden-import=customtkinter",
        "--hidden-import=PIL",
        "--hidden-import=requests",
        "--hidden-import=threading",
        "--hidden-import=queue",
        "--hidden-import=json",
        "--hidden-import=datetime",
        "--hidden-import=tkinter",
        "--hidden-import=tkinter.filedialog",
        "--hidden-import=tkinter.messagebox",
        "--uac-admin",
        "--clean",
        "--noconfirm",
        "proxy_fetcher_gui.py"
    ]
    
    print("🔄 开始构建...")
    print("命令:", " ".join(cmd))
    
    try:
        # 直接运行，不捕获输出以避免编码问题
        result = subprocess.run(cmd)
        
        if result.returncode == 0:
            print("\n✅ 构建完成！")
            
            # 检查输出文件
            exe_path = Path("dist/ProxyFetcher_v2.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 输出文件: {exe_path}")
                print(f"📏 文件大小: {size_mb:.1f} MB")
                print("\n🎯 测试建议:")
                print("1. 运行 dist/ProxyFetcher_v2.exe")
                print("2. 检查新的统一界面布局")
                print("3. 测试16号字体显示效果")
                print("4. 验证按钮样式是否符合要求")
            else:
                print("❌ 未找到输出文件")
        else:
            print(f"\n❌ 构建失败，退出码: {result.returncode}")
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")

if __name__ == "__main__":
    main()
