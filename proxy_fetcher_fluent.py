#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理节点获取器 - Windows 11 Fluent Design Edition
严格遵循Microsoft官方设计规范
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import filedialog, messagebox
import threading
import queue
import os
import sys
# import json  # 不再需要，已禁用配置文件功能
from datetime import datetime
from typing import Optional

# 导入设计系统和原始功能
from fluent_design_system import FluentDesignSystem, FluentComponents
from proxy_fetcher import ProxyFetcher

# 设置CustomTkinter - 固定light主题
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")


class FluentProgressRing(ctk.CTkFrame):
    """优化的Fluent Design风格进度环"""

    def __init__(self, parent, size=32, **kwargs):
        super().__init__(parent, **kwargs)
        self.size = size
        self.angle = 0
        self.is_running = False
        self._animation_id = None

        # 使用设计系统颜色
        theme_colors = FluentDesignSystem.get_theme_colors()

        self.canvas = tk.Canvas(
            self,
            width=size,
            height=size,
            highlightthickness=0,
            bg=theme_colors["background_tertiary"]
        )
        self.canvas.pack(padx=4, pady=4)

    def start(self):
        """开始动画"""
        if not self.is_running:
            self.is_running = True
            self._animate()

    def stop(self):
        """停止动画"""
        self.is_running = False
        if self._animation_id:
            self.after_cancel(self._animation_id)
            self._animation_id = None
        self.canvas.delete("all")

    def _animate(self):
        """优化的动画循环"""
        if not self.is_running:
            return

        self.canvas.delete("all")

        center = self.size // 2
        radius = center - 3

        # 绘制进度环
        self.canvas.create_arc(
            center - radius, center - radius,
            center + radius, center + radius,
            start=self.angle, extent=90,
            outline=FluentDesignSystem.get_accent_color("primary"),
            width=2, style="arc"
        )

        self.angle = (self.angle + 12) % 360

        # 优化动画间隔
        self._animation_id = self.after(100, self._animate)


class ProxyFetcherFluentApp:
    """基于Fluent Design的代理获取器应用"""

    def __init__(self):
        self.root = ctk.CTk()
        self.setup_window()
        self.setup_variables()
        self.setup_ui()
        self.setup_queue()

        # 原始获取器实例
        self.fetcher = None
        self.fetch_thread = None

    def setup_window(self):
        """设置主窗口"""
        self.root.title("代理节点获取器 - Fluent Design")
        # 优化窗口大小，确保所有按钮可见
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)

        # 设置窗口图标
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        # 居中显示
        self.center_window()

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def setup_variables(self):
        """设置变量"""
        self.api_url = tk.StringVar(value="https://ohayoo-distribute.hf.space/api/v1/subscribe?token=335wsxrnx0ig4pg6&target=clash&list=1")
        self.fetch_count = tk.IntVar(value=3)
        self.thread_count = tk.IntVar(value=3)
        self.interval = tk.IntVar(value=1)
        self.output_dir = tk.StringVar(value=os.getcwd())

        # 状态变量
        self.is_fetching = tk.BooleanVar(value=False)
        self.current_progress = tk.StringVar(value="就绪")

    def setup_queue(self):
        """设置消息队列"""
        self.message_queue = queue.Queue()
        self.check_queue()

    def check_queue(self):
        """优化的消息队列检查"""
        try:
            # 批量处理消息以提高性能
            messages_processed = 0
            while messages_processed < 10:  # 限制每次处理的消息数量
                message = self.message_queue.get_nowait()
                self.update_log(message)
                messages_processed += 1
        except queue.Empty:
            pass
        finally:
            # 优化检查频率
            self.root.after(50, self.check_queue)

    def setup_ui(self):
        """设置用户界面"""
        # 获取主题颜色
        theme_colors = FluentDesignSystem.get_theme_colors()

        # 主容器 - 使用背景主色
        self.main_container = ctk.CTkFrame(
            self.root,
            corner_radius=0,
            fg_color=theme_colors["background_primary"]
        )
        self.main_container.pack(
            fill="both",
            expand=True,
            padx=FluentDesignSystem.get_spacing("lg"),
            pady=FluentDesignSystem.get_spacing("lg")
        )

        # 创建界面
        self.create_header()
        self.create_main_content()

    def create_header(self):
        """创建头部区域"""
        theme_colors = FluentDesignSystem.get_theme_colors()

        # 头部容器
        header_frame = ctk.CTkFrame(
            self.main_container,
            fg_color="transparent",
            height=80
        )
        header_frame.pack(
            fill="x",
            pady=(0, FluentDesignSystem.get_spacing("lg"))
        )
        header_frame.pack_propagate(False)

        # 应用标题
        title_label = ctk.CTkLabel(
            header_frame,
            text="代理节点获取器",
            font=FluentDesignSystem.get_font("title", "bold"),
            text_color=theme_colors["text_primary"]
        )
        title_label.pack(side="left", pady=FluentDesignSystem.get_spacing("md"))

    def create_main_content(self):
        """创建主要内容区域"""
        # 主内容容器
        content_container = ctk.CTkFrame(
            self.main_container,
            fg_color="transparent"
        )
        content_container.pack(fill="both", expand=True)

        # 左侧配置区域
        left_panel = self.create_config_panel(content_container)
        left_panel.pack(
            side="left",
            fill="both",
            expand=True,
            padx=(0, FluentDesignSystem.get_spacing("md"))
        )

        # 右侧日志区域
        right_panel = self.create_log_panel(content_container)
        right_panel.pack(
            side="right",
            fill="both",
            expand=True,
            padx=(FluentDesignSystem.get_spacing("md"), 0)
        )

    def create_config_panel(self, parent):
        """创建配置面板"""
        # 使用Fluent卡片组件
        config_card = FluentComponents.create_card(parent, "large")

        # 卡片标题
        title_frame = ctk.CTkFrame(config_card, fg_color="transparent")
        title_frame.pack(
            fill="x",
            padx=FluentDesignSystem.get_spacing("lg"),
            pady=(FluentDesignSystem.get_spacing("lg"), FluentDesignSystem.get_spacing("md"))
        )

        theme_colors = FluentDesignSystem.get_theme_colors()
        ctk.CTkLabel(
            title_frame,
            text="获取配置",
            font=FluentDesignSystem.get_font("subtitle", "bold"),
            text_color=theme_colors["text_primary"]
        ).pack(anchor="w")

        # 配置表单
        self.create_config_form(config_card)

        # 控制按钮
        self.create_control_buttons(config_card)

        # 状态指示器
        self.create_status_indicator(config_card)

        return config_card

    def create_config_form(self, parent):
        """创建配置表单"""
        theme_colors = FluentDesignSystem.get_theme_colors()

        # 表单容器
        form_frame = ctk.CTkFrame(parent, fg_color="transparent")
        form_frame.pack(
            fill="x",
            padx=FluentDesignSystem.get_spacing("lg"),
            pady=FluentDesignSystem.get_spacing("md")
        )

        # 配置项间距
        item_spacing = FluentDesignSystem.get_spacing("md")

        # 获取次数
        count_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        count_frame.pack(fill="x", pady=(0, item_spacing))

        ctk.CTkLabel(
            count_frame,
            text="获取次数",
            font=FluentDesignSystem.get_font("body"),
            text_color=theme_colors["text_primary"]
        ).pack(anchor="w", pady=(0, FluentDesignSystem.get_spacing("xs")))

        self.count_entry = FluentComponents.create_entry(
            count_frame,
            placeholder="请输入获取次数",
            width=200,
            height=40
        )
        self.count_entry.pack(anchor="w")
        self.count_entry.insert(0, str(self.fetch_count.get()))

        # 线程数
        thread_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        thread_frame.pack(fill="x", pady=(0, item_spacing))

        ctk.CTkLabel(
            thread_frame,
            text="线程数",
            font=FluentDesignSystem.get_font("body"),
            text_color=theme_colors["text_primary"]
        ).pack(anchor="w", pady=(0, FluentDesignSystem.get_spacing("xs")))

        self.thread_entry = FluentComponents.create_entry(
            thread_frame,
            placeholder="请输入线程数",
            width=200,
            height=40
        )
        self.thread_entry.pack(anchor="w")
        self.thread_entry.insert(0, str(self.thread_count.get()))

        # 间隔时间
        interval_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        interval_frame.pack(fill="x", pady=(0, item_spacing))

        ctk.CTkLabel(
            interval_frame,
            text="间隔时间 (秒)",
            font=FluentDesignSystem.get_font("body"),
            text_color=theme_colors["text_primary"]
        ).pack(anchor="w", pady=(0, FluentDesignSystem.get_spacing("xs")))

        self.interval_entry = FluentComponents.create_entry(
            interval_frame,
            placeholder="请输入间隔时间",
            width=200,
            height=40
        )
        self.interval_entry.pack(anchor="w")
        self.interval_entry.insert(0, str(self.interval.get()))

        # 输出目录
        output_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        output_frame.pack(fill="x", pady=(0, item_spacing))

        ctk.CTkLabel(
            output_frame,
            text="输出目录",
            font=FluentDesignSystem.get_font("body"),
            text_color=theme_colors["text_primary"]
        ).pack(anchor="w", pady=(0, FluentDesignSystem.get_spacing("xs")))

        dir_input_frame = ctk.CTkFrame(output_frame, fg_color="transparent")
        dir_input_frame.pack(fill="x")

        self.output_entry = FluentComponents.create_entry(
            dir_input_frame,
            placeholder="选择输出目录",
            height=40
        )
        self.output_entry.pack(
            side="left",
            fill="x",
            expand=True,
            padx=(0, FluentDesignSystem.get_spacing("sm"))
        )
        self.output_entry.insert(0, self.output_dir.get())

        browse_btn = FluentComponents.create_button(
            dir_input_frame,
            text="浏览",
            style="subtle",
            command=self.browse_output_dir,
            width=80,
            height=40
        )
        browse_btn.pack(side="right")

        # API地址
        api_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        api_frame.pack(fill="x", pady=(item_spacing, 0))

        ctk.CTkLabel(
            api_frame,
            text="API地址",
            font=FluentDesignSystem.get_font("body"),
            text_color=theme_colors["text_primary"]
        ).pack(anchor="w", pady=(0, FluentDesignSystem.get_spacing("xs")))

        self.api_entry = FluentComponents.create_entry(
            api_frame,
            placeholder="请输入API地址",
            height=40
        )
        self.api_entry.pack(fill="x")
        self.api_entry.insert(0, self.api_url.get())

    def create_control_buttons(self, parent):
        """创建控制按钮"""
        # 按钮容器 - 增加高度确保按钮可见
        button_frame = ctk.CTkFrame(parent, fg_color="transparent", height=100)
        button_frame.pack(
            fill="x",
            padx=FluentDesignSystem.get_spacing("lg"),
            pady=FluentDesignSystem.get_spacing("xl")
        )
        button_frame.pack_propagate(False)

        # 按钮组
        button_group = ctk.CTkFrame(button_frame, fg_color="transparent")
        button_group.pack()

        # 开始按钮 - 使用强调色样式
        self.start_btn = FluentComponents.create_button(
            button_group,
            text="🚀 开始获取",
            style="accent",
            command=self.start_fetch,
            width=160,
            height=52
        )
        self.start_btn.pack(
            side="left",
            padx=(0, FluentDesignSystem.get_spacing("lg")),
            pady=FluentDesignSystem.get_spacing("md")
        )

        # 停止按钮
        self.stop_btn = FluentComponents.create_button(
            button_group,
            text="⏹️ 停止",
            style="subtle",
            command=self.stop_fetch,
            width=120,
            height=52,
            state="disabled"
        )
        self.stop_btn.pack(
            side="left",
            padx=FluentDesignSystem.get_spacing("lg"),
            pady=FluentDesignSystem.get_spacing("md")
        )

        # 清空日志按钮
        clear_btn = FluentComponents.create_button(
            button_group,
            text="🗑️ 清空",
            style="subtle",
            command=self.clear_logs,
            width=120,
            height=52
        )
        clear_btn.pack(
            side="left",
            padx=FluentDesignSystem.get_spacing("lg"),
            pady=FluentDesignSystem.get_spacing("md")
        )

    def create_status_indicator(self, parent):
        """创建状态指示器"""
        theme_colors = FluentDesignSystem.get_theme_colors()

        # 状态容器
        status_frame = ctk.CTkFrame(parent, fg_color="transparent")
        status_frame.pack(
            fill="x",
            padx=FluentDesignSystem.get_spacing("lg"),
            pady=(0, FluentDesignSystem.get_spacing("lg"))
        )

        # 状态标题
        ctk.CTkLabel(
            status_frame,
            text="执行状态",
            font=FluentDesignSystem.get_font("body_strong", "bold"),
            text_color=theme_colors["text_primary"]
        ).pack(anchor="w", pady=(0, FluentDesignSystem.get_spacing("sm")))

        # 状态指示器
        indicator_frame = ctk.CTkFrame(status_frame, fg_color="transparent")
        indicator_frame.pack(fill="x")

        self.status_label = ctk.CTkLabel(
            indicator_frame,
            textvariable=self.current_progress,
            font=FluentDesignSystem.get_font("body"),
            text_color=theme_colors["text_secondary"]
        )
        self.status_label.pack(side="left")

        # 进度环
        self.progress_ring = FluentProgressRing(
            indicator_frame,
            size=32,
            fg_color="transparent"
        )
        self.progress_ring.pack(side="right", padx=FluentDesignSystem.get_spacing("sm"))

    def create_log_panel(self, parent):
        """创建日志面板"""
        # 使用Fluent卡片组件
        log_card = FluentComponents.create_card(parent, "large")

        # 卡片标题
        title_frame = ctk.CTkFrame(log_card, fg_color="transparent")
        title_frame.pack(
            fill="x",
            padx=FluentDesignSystem.get_spacing("lg"),
            pady=(FluentDesignSystem.get_spacing("lg"), FluentDesignSystem.get_spacing("md"))
        )

        theme_colors = FluentDesignSystem.get_theme_colors()

        # 标题和控制按钮
        ctk.CTkLabel(
            title_frame,
            text="执行日志",
            font=FluentDesignSystem.get_font("subtitle", "bold"),
            text_color=theme_colors["text_primary"]
        ).pack(side="left")

        # 导出按钮
        export_btn = FluentComponents.create_button(
            title_frame,
            text="📤 导出",
            style="subtle",
            command=self.export_logs,
            width=80,
            height=32
        )
        export_btn.pack(side="right")

        # 日志显示区域
        log_container = ctk.CTkFrame(log_card, fg_color="transparent")
        log_container.pack(
            fill="both",
            expand=True,
            padx=FluentDesignSystem.get_spacing("lg"),
            pady=(0, FluentDesignSystem.get_spacing("lg"))
        )

        # 日志文本框
        self.log_text = ctk.CTkTextbox(
            log_container,
            corner_radius=FluentDesignSystem.get_corner_radius("small"),
            font=FluentDesignSystem.get_font("body"),
            fg_color=theme_colors["control_fill_default"],
            text_color=theme_colors["text_primary"],
            wrap="word"
        )
        self.log_text.pack(fill="both", expand=True)

        return log_card

    # === 事件处理方法 ===

    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(
            title="选择输出目录",
            initialdir=self.output_dir.get()
        )
        if directory:
            self.output_dir.set(directory)
            self.output_entry.delete(0, "end")
            self.output_entry.insert(0, directory)

    def clear_logs(self):
        """清空日志"""
        self.log_text.delete("1.0", "end")

    def export_logs(self):
        """导出日志"""
        try:
            filename = filedialog.asksaveasfilename(
                title="导出日志",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if filename:
                log_content = self.log_text.get("1.0", "end-1c")
                with open(filename, "w", encoding="utf-8") as f:
                    f.write(log_content)
                messagebox.showinfo("成功", f"日志已导出到: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"导出日志失败: {e}")

    def update_log(self, message):
        """更新日志显示"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.insert("end", formatted_message)
        self.log_text.see("end")

    def get_form_values(self):
        """获取表单值"""
        try:
            fetch_count = int(self.count_entry.get() or "3")
            thread_count = int(self.thread_entry.get() or "3")
            interval = int(self.interval_entry.get() or "1")
            output_dir = self.output_entry.get() or os.getcwd()
            api_url = self.api_entry.get()

            return fetch_count, thread_count, interval, output_dir, api_url
        except ValueError:
            raise ValueError("请输入有效的数字值")

    def validate_inputs(self, fetch_count, thread_count, interval, output_dir, api_url):
        """验证输入"""
        if fetch_count <= 0:
            raise ValueError("获取次数必须大于0")

        if thread_count <= 0:
            raise ValueError("线程数必须大于0")

        if thread_count > fetch_count:
            raise ValueError("线程数不能大于获取次数")

        if interval < 0:
            raise ValueError("间隔时间不能为负数")

        if not api_url.strip():
            raise ValueError("API地址不能为空")

        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
            except Exception as e:
                raise ValueError(f"无法创建输出目录: {e}")

    def start_fetch(self):
        """开始获取"""
        try:
            # 获取和验证输入
            fetch_count, thread_count, interval, output_dir, api_url = self.get_form_values()
            self.validate_inputs(fetch_count, thread_count, interval, output_dir, api_url)

            # 切换到输出目录
            original_dir = os.getcwd()
            os.chdir(output_dir)

            # 更新UI状态
            self.is_fetching.set(True)
            self.start_btn.configure(state="disabled")
            self.stop_btn.configure(state="normal")
            self.current_progress.set("正在获取...")
            self.progress_ring.start()

            # 清空日志
            self.clear_logs()
            self.update_log("🚀 开始获取代理节点...")

            # 创建获取器实例
            self.fetcher = ProxyFetcher(api_url)

            # 启动获取线程
            self.fetch_thread = threading.Thread(
                target=self._fetch_worker,
                args=(fetch_count, thread_count, interval, original_dir),
                daemon=True
            )
            self.fetch_thread.start()

        except ValueError as e:
            messagebox.showerror("输入错误", str(e))
        except Exception as e:
            messagebox.showerror("错误", f"启动获取失败: {e}")
            self.stop_fetch()

    def _fetch_worker(self, fetch_count, thread_count, interval, original_dir):
        """获取工作线程"""
        try:
            # 重写ProxyFetcher的safe_print方法，使其输出到GUI
            def gui_safe_print(message):
                self.message_queue.put(message)

            self.fetcher.safe_print = gui_safe_print

            # 执行获取
            self.fetcher.run_fetch_cycle(fetch_count, thread_count, interval)

            # 完成后更新状态
            self.message_queue.put("✅ 获取完成！")

        except Exception as e:
            self.message_queue.put(f"❌ 获取失败: {e}")
        finally:
            # 恢复原始目录
            os.chdir(original_dir)

            # 更新UI状态
            self.root.after(0, self._fetch_completed)

    def _fetch_completed(self):
        """获取完成后的UI更新"""
        self.is_fetching.set(False)
        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")
        self.current_progress.set("获取完成")
        self.progress_ring.stop()

    def stop_fetch(self):
        """停止获取"""
        self.is_fetching.set(False)
        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")
        self.current_progress.set("已停止")
        self.progress_ring.stop()

        if self.fetch_thread and self.fetch_thread.is_alive():
            self.update_log("⚠️ 正在尝试停止获取...")

    def save_settings(self):
        """保存设置 - 已禁用，不生成配置文件"""
        # 不再保存设置到文件
        pass

    def load_settings(self):
        """加载设置 - 已禁用，不读取配置文件"""
        # 不再从文件加载设置，使用默认值
        pass

    def run(self):
        """运行应用"""
        # 不再加载设置文件，使用默认值

        # 设置关闭处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动主循环
        self.root.mainloop()

    def on_closing(self):
        """关闭应用时的处理"""
        # 不再保存设置到文件

        # 停止获取（如果正在进行）
        if self.is_fetching.get():
            self.stop_fetch()

        # 关闭窗口
        self.root.destroy()


def main():
    """主函数"""
    try:
        app = ProxyFetcherFluentApp()
        app.run()
    except Exception as e:
        print(f"应用启动失败: {e}")
        messagebox.showerror("错误", f"应用启动失败: {e}")


if __name__ == "__main__":
    main()
