# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['proxy_fetcher_fluent.py'],
    pathex=[],
    binaries=[],
    datas=[('proxy_fetcher.py', '.'), ('fluent_design_system.py', '.')],
    hiddenimports=['customtkinter', 'PIL', 'requests', 'threading', 'queue', 'json', 'datetime', 'tkinter', 'tkinter.filedialog', 'tkinter.messagebox', 'typing'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='ProxyFetcher_FluentDesign',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    uac_admin=True,
)
