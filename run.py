#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理节点获取器启动脚本
快速启动GUI应用程序
"""

import sys
import os
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖项"""
    required_packages = [
        "customtkinter",
        "requests",
        "Pillow"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def install_dependencies():
    """自动安装依赖"""
    print("🔄 正在安装依赖包...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 代理节点获取器启动器")
    print("=" * 40)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查依赖
    if not check_dependencies():
        choice = input("\n是否自动安装依赖? (y/N): ").lower()
        if choice in ['y', 'yes']:
            if not install_dependencies():
                return
        else:
            return
    
    print("✅ 所有依赖已满足")
    
    # 检查必要文件
    required_files = ["proxy_fetcher.py", "proxy_fetcher_gui.py"]
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少以下文件:")
        for file in missing_files:
            print(f"   - {file}")
        return
    
    print("✅ 所有文件完整")
    
    # 启动GUI应用
    print("\n🎯 启动GUI应用...")
    try:
        from proxy_fetcher_gui import main as gui_main
        gui_main()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n请检查错误信息并重试")

if __name__ == "__main__":
    main()
