#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fluent Design版本打包脚本
基于Microsoft官方设计规范的代理获取器
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    """主函数"""
    print("🎨 代理节点获取器 - Fluent Design Edition 打包工具")
    print("=" * 60)
    print("基于Microsoft官方设计规范")
    print("参考文档：")
    print("- Windows 中的颜色")
    print("- 亚克力材料")
    print("- 采用 XAML 的响应式布局")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        "proxy_fetcher_fluent.py",
        "fluent_design_system.py", 
        "proxy_fetcher.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return
    
    print("✅ 文件检查完成")
    
    # 构建命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--name=ProxyFetcher_FluentDesign",
        "--onefile",
        "--windowed",
        
        # 添加数据文件
        "--add-data=proxy_fetcher.py;.",
        "--add-data=fluent_design_system.py;.",
        
        # 隐藏导入
        "--hidden-import=customtkinter",
        "--hidden-import=PIL",
        "--hidden-import=requests",
        "--hidden-import=threading",
        "--hidden-import=queue",
        "--hidden-import=json",
        "--hidden-import=datetime",
        "--hidden-import=tkinter",
        "--hidden-import=tkinter.filedialog",
        "--hidden-import=tkinter.messagebox",
        "--hidden-import=typing",
        
        # Windows特定设置
        "--uac-admin",
        "--clean",
        "--noconfirm",
        
        # 主文件
        "proxy_fetcher_fluent.py"
    ]
    
    print("🔄 开始构建 Fluent Design 版本...")
    print("特性:")
    print("✨ Windows 11 Fluent Design 系统")
    print("🎨 官方颜色规范")
    print("📐 响应式布局")
    print("🌓 浅色/深色主题支持")
    print("🔄 流畅动画效果")
    print("📱 现代化界面组件")
    print()
    
    try:
        # 直接运行，显示实时输出
        result = subprocess.run(cmd, check=True)
        
        print("\n✅ 构建完成！")
        
        # 检查输出文件
        exe_path = Path("dist/ProxyFetcher_FluentDesign.exe")
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"📁 输出文件: {exe_path}")
            print(f"📏 文件大小: {size_mb:.1f} MB")
            
            print("\n🎯 Fluent Design 特性验证清单:")
            print("□ 检查 Windows 11 风格界面")
            print("□ 验证官方颜色系统")
            print("□ 测试主题切换功能")
            print("□ 确认圆角和间距规范")
            print("□ 验证字体系统 (Segoe UI)")
            print("□ 测试响应式布局")
            print("□ 检查动画效果")
            print("□ 验证卡片组件样式")
            print("□ 测试按钮状态变化")
            print("□ 确认无障碍对比度")
            
            print("\n🚀 设计规范遵循:")
            print("✅ Microsoft Learn 官方文档")
            print("✅ Windows 11 Fluent Design")
            print("✅ 8px 网格系统")
            print("✅ 官方颜色调色板")
            print("✅ Segoe UI 字体系统")
            print("✅ 标准圆角规范")
            print("✅ 层次阴影系统")
            print("✅ 响应式断点")
            
        else:
            print("❌ 未找到输出文件")
            
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 构建失败，退出码: {e.returncode}")
        print("请检查错误信息并重试")
    except Exception as e:
        print(f"\n❌ 构建过程出错: {e}")

if __name__ == "__main__":
    main()
