#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的PyInstaller打包脚本
解决路径问题的版本
"""

import subprocess
import sys
import os
from pathlib import Path

def check_files():
    """检查必要文件"""
    required_files = [
        "proxy_fetcher_gui.py",
        "proxy_fetcher.py",
        "requirements.txt"
    ]

    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)

    if missing_files:
        print("❌ 缺少以下文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False

    return True

def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['proxy_fetcher_gui.py'],
    pathex=[],
    binaries=[],
    datas=[('proxy_fetcher.py', '.')],
    hiddenimports=[
        'customtkinter',
        'PIL',
        'PIL._tkinter_finder',
        'requests',
        'threading',
        'queue',
        'json',
        'datetime',
        'tkinter',
        'tkinter.filedialog',
        'tkinter.messagebox'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=2,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='ProxyFetcher',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    uac_admin=True,
    icon=None,
)
'''

    with open("ProxyFetcher.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)

    print("✅ Spec文件已创建")

def build_with_pyinstaller():
    """使用PyInstaller构建"""
    try:
        # 方法1：使用spec文件
        print("🔄 使用spec文件构建...")

        # 修复编码问题 - 使用系统默认编码
        result = subprocess.run([
            sys.executable, "-m", "PyInstaller",
            "ProxyFetcher.spec",
            "--clean",
            "--noconfirm"
        ], capture_output=True, text=True, encoding='gbk', errors='ignore')

        if result.returncode == 0:
            print("✅ 构建成功！")
            return True
        else:
            print("❌ spec文件构建失败，尝试直接构建...")
            if result.stderr:
                print(f"错误信息: {result.stderr}")

            # 方法2：直接命令行构建
            print("🔄 使用直接命令构建...")
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--name=ProxyFetcher",
                "--onefile",
                "--windowed",
                "--add-data=proxy_fetcher.py;.",
                "--hidden-import=customtkinter",
                "--hidden-import=PIL",
                "--hidden-import=requests",
                "--uac-admin",
                "--clean",
                "--noconfirm",
                "proxy_fetcher_gui.py"
            ]

            result2 = subprocess.run(cmd, capture_output=True, text=True, encoding='gbk', errors='ignore')

            if result2.returncode == 0:
                print("✅ 直接构建成功！")
                return True
            else:
                print("❌ 直接构建也失败")
                if result2.stderr:
                    print(f"错误信息: {result2.stderr}")
                return False

    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def main():
    """主函数"""
    print("🚀 代理节点获取器 - 简化打包工具")
    print("=" * 50)

    # 检查Python版本
    if sys.version_info < (3, 8):
        print(f"❌ Python版本过低: {sys.version}")
        return

    print(f"✅ Python版本: {sys.version.split()[0]}")

    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ 未安装PyInstaller")
        print("请运行: pip install pyinstaller")
        return

    # 检查文件
    if not check_files():
        return

    print("✅ 文件检查完成")

    # 创建spec文件
    create_spec_file()

    # 构建应用
    if build_with_pyinstaller():
        print("\n🎉 构建完成！")

        # 检查输出文件
        exe_path = Path("dist/ProxyFetcher.exe")
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"📁 输出文件: {exe_path}")
            print(f"📏 文件大小: {size_mb:.1f} MB")
            print("\n🎯 测试建议:")
            print("1. 运行 dist/ProxyFetcher.exe 测试基本功能")
            print("2. 检查界面显示是否正常")
            print("3. 测试代理获取功能")
            print("4. 验证主题切换功能")
        else:
            print("❌ 未找到输出文件，请检查构建日志")
    else:
        print("\n❌ 构建失败")
        print("请检查以上错误信息并重试")

if __name__ == "__main__":
    main()
