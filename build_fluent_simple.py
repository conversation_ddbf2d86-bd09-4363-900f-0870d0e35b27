#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fluent Design版本简化打包脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    """主函数"""
    print("🎨 代理节点获取器 - Fluent Design Edition")
    print("=" * 50)
    print("基于Microsoft官方Windows 11设计规范")
    print("=" * 50)

    # 检查文件
    required_files = [
        "proxy_fetcher_fluent.py",
        "fluent_design_system.py",
        "proxy_fetcher.py"
    ]

    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 缺少文件: {file}")
            return

    print("✅ 文件检查完成")

    # 简化的构建命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--name=ProxyFetcher_FluentDesign",
        "--onefile",
        "--windowed",
        "--add-data=proxy_fetcher.py;.",
        "--add-data=fluent_design_system.py;.",
        "--hidden-import=customtkinter",
        "--hidden-import=PIL",
        "--hidden-import=requests",
        "--clean",
        "--noconfirm",
        "proxy_fetcher_fluent.py"
    ]

    print("🔄 开始构建...")

    try:
        result = subprocess.run(cmd)

        if result.returncode == 0:
            print("\n✅ 构建完成！")

            exe_path = Path("dist/ProxyFetcher_FluentDesign.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 输出文件: {exe_path}")
                print(f"📏 文件大小: {size_mb:.1f} MB")

                print("\n🎯 优化特性:")
                print("✅ Windows 11 官方颜色系统")
                print("✅ Segoe UI 字体")
                print("✅ 8px 网格间距")
                print("✅ 标准圆角 (4px/8px/12px)")
                print("✅ 固定浅色主题")
                print("✅ 优化窗口大小 (1400x900)")
                print("✅ 优化响应速度")
                print("✅ 优化内存使用")
                print("✅ 按钮可见性优化")
                print("✅ 无配置文件生成")

            else:
                print("❌ 未找到输出文件")
        else:
            print(f"\n❌ 构建失败，退出码: {result.returncode}")

    except Exception as e:
        print(f"\n❌ 构建出错: {e}")

if __name__ == "__main__":
    main()
