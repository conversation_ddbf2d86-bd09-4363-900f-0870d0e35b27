#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller 打包配置脚本
用于生成Windows 11 Fluent Design风格的可执行文件
"""

import PyInstaller.__main__
import os
import sys
from pathlib import Path

def build_app():
    """构建应用程序"""

    # 获取当前目录
    current_dir = Path(__file__).parent

    # 检查必要文件是否存在
    required_files = ["proxy_fetcher_gui.py", "proxy_fetcher.py"]
    for file in required_files:
        if not (current_dir / file).exists():
            print(f"❌ 缺少必要文件: {file}")
            return False

    # 定义打包参数
    pyinstaller_args = [
        # 主脚本
        str(current_dir / "proxy_fetcher_gui.py"),

        # 输出选项
        "--name=ProxyFetcher",
        "--onefile",  # 单文件模式
        "--windowed",  # 无控制台窗口

        # 图标和资源
        # "--icon=icon.ico",  # 如果有图标文件

        # 包含的数据文件 - 修复路径问题
        f"--add-data={current_dir / 'proxy_fetcher.py'};.",

        # 隐藏导入
        "--hidden-import=customtkinter",
        "--hidden-import=PIL",
        "--hidden-import=PIL._tkinter_finder",
        "--hidden-import=requests",
        "--hidden-import=threading",
        "--hidden-import=queue",
        "--hidden-import=json",
        "--hidden-import=datetime",

        # 输出目录
        "--distpath=dist",
        "--workpath=build",
        "--specpath=build",

        # 优化选项
        "--optimize=2",
        # "--strip",  # 在Windows上可能导致问题，暂时移除

        # Windows特定选项
        "--uac-admin",  # 请求管理员权限（用于亚克力效果）

        # 版本信息
        "--version-file=version_info.txt",

        # 清理选项
        "--clean",
        "--noconfirm",

        # 调试选项（发布时可移除）
        # "--debug=all",
        # "--console",  # 调试时显示控制台
    ]

    print("开始构建应用程序...")
    print(f"参数: {' '.join(pyinstaller_args)}")

    try:
        PyInstaller.__main__.run(pyinstaller_args)
        print("\n✅ 构建完成！")
        print(f"可执行文件位置: {current_dir / 'dist' / 'ProxyFetcher.exe'}")

    except Exception as e:
        print(f"\n❌ 构建失败: {e}")
        return False

    return True

def create_version_info():
    """创建版本信息文件"""
    version_info = """# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
    # Set not needed items to zero 0.
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    # Contains a bitmask that specifies the valid bits 'flags'r
    mask=0x3f,
    # Contains a bitmask that specifies the Boolean attributes of the file.
    flags=0x0,
    # The operating system for which this file was designed.
    # 0x4 - NT and there is no need to change it.
    OS=0x4,
    # The general type of file.
    # 0x1 - the file is an application.
    fileType=0x1,
    # The function of the file.
    # 0x0 - the function is not defined for this fileType
    subtype=0x0,
    # Creation date and time stamp.
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Fluent Design Studio'),
        StringStruct(u'FileDescription', u'代理节点获取器 - Fluent Design Edition'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'ProxyFetcher'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024'),
        StringStruct(u'OriginalFilename', u'ProxyFetcher.exe'),
        StringStruct(u'ProductName', u'代理节点获取器'),
        StringStruct(u'ProductVersion', u'*******')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)"""

    with open("version_info.txt", "w", encoding="utf-8") as f:
        f.write(version_info)

    print("✅ 版本信息文件已创建")

def create_nsis_installer():
    """创建NSIS安装脚本"""
    nsis_script = """
; 代理节点获取器安装脚本
; 使用NSIS创建现代化安装程序

!include "MUI2.nsh"
!include "FileFunc.nsh"

; 应用信息
!define APP_NAME "代理节点获取器"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "Fluent Design Studio"
!define APP_URL "https://github.com/your-repo"
!define APP_EXECUTABLE "ProxyFetcher.exe"

; 安装程序设置
Name "${APP_NAME}"
OutFile "ProxyFetcher_Setup.exe"
InstallDir "$PROGRAMFILES\\${APP_NAME}"
InstallDirRegKey HKCU "Software\\${APP_NAME}" ""
RequestExecutionLevel admin

; 界面设置
!define MUI_ABORTWARNING
!define MUI_ICON "icon.ico"
!define MUI_UNICON "icon.ico"

; 页面
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; 语言
!insertmacro MUI_LANGUAGE "SimpChinese"

; 安装部分
Section "主程序" SecMain
    SetOutPath "$INSTDIR"
    File "dist\\${APP_EXECUTABLE}"

    ; 创建快捷方式
    CreateDirectory "$SMPROGRAMS\\${APP_NAME}"
    CreateShortCut "$SMPROGRAMS\\${APP_NAME}\\${APP_NAME}.lnk" "$INSTDIR\\${APP_EXECUTABLE}"
    CreateShortCut "$DESKTOP\\${APP_NAME}.lnk" "$INSTDIR\\${APP_EXECUTABLE}"

    ; 写入注册表
    WriteRegStr HKCU "Software\\${APP_NAME}" "" $INSTDIR
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "DisplayName" "${APP_NAME}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "UninstallString" "$INSTDIR\\uninstall.exe"
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "NoModify" 1
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "NoRepair" 1

    ; 创建卸载程序
    WriteUninstaller "$INSTDIR\\uninstall.exe"
SectionEnd

; 卸载部分
Section "Uninstall"
    Delete "$INSTDIR\\${APP_EXECUTABLE}"
    Delete "$INSTDIR\\uninstall.exe"

    Delete "$SMPROGRAMS\\${APP_NAME}\\${APP_NAME}.lnk"
    Delete "$DESKTOP\\${APP_NAME}.lnk"
    RMDir "$SMPROGRAMS\\${APP_NAME}"
    RMDir "$INSTDIR"

    DeleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}"
    DeleteRegKey HKCU "Software\\${APP_NAME}"
SectionEnd
"""

    with open("installer.nsi", "w", encoding="utf-8") as f:
        f.write(nsis_script)

    print("✅ NSIS安装脚本已创建")

def main():
    """主函数"""
    print("🚀 代理节点获取器构建工具")
    print("=" * 50)

    # 检查依赖
    try:
        import PyInstaller
        print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ 未安装PyInstaller，请运行: pip install pyinstaller")
        return

    # 创建必要文件
    create_version_info()
    create_nsis_installer()

    # 构建应用
    if build_app():
        print("\n📦 构建完成！")
        print("下一步:")
        print("1. 测试 dist/ProxyFetcher.exe")
        print("2. 使用NSIS编译 installer.nsi 创建安装包")
        print("3. 或使用以下命令创建MSIX包:")
        print("   makeappx pack /d dist /p ProxyFetcher.msix")

if __name__ == "__main__":
    main()
