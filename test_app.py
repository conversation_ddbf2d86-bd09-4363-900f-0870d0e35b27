#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序测试脚本
验证GUI应用的各项功能
"""

import sys
import os
import unittest
import threading
import time
from unittest.mock import Mock, patch

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import customtkinter as ctk
    from proxy_fetcher import ProxyFetcher
    from proxy_fetcher_gui import ProxyFetcherGUI, FluentColors, FluentProgressRing
    from fluent_theme import FluentTheme
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)

class TestProxyFetcher(unittest.TestCase):
    """测试ProxyFetcher类"""
    
    def setUp(self):
        """测试前准备"""
        self.api_url = "https://httpbin.org/json"  # 测试API
        self.fetcher = ProxyFetcher(self.api_url)
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.fetcher.api_url, self.api_url)
        self.assertIsNotNone(self.fetcher.session)
        self.assertIsNotNone(self.fetcher.print_lock)
    
    def test_safe_print(self):
        """测试线程安全打印"""
        # 这个测试主要验证方法不会抛出异常
        try:
            self.fetcher.safe_print("测试消息")
        except Exception as e:
            self.fail(f"safe_print抛出异常: {e}")

class TestFluentColors(unittest.TestCase):
    """测试Fluent颜色类"""
    
    def test_color_constants(self):
        """测试颜色常量"""
        self.assertEqual(FluentColors.ACCENT_PRIMARY, "#0078D4")
        self.assertEqual(FluentColors.SUCCESS, "#107C10")
        self.assertEqual(FluentColors.ERROR, "#D13438")
    
    def test_color_format(self):
        """测试颜色格式"""
        colors = [
            FluentColors.ACCENT_PRIMARY,
            FluentColors.BACKGROUND_PRIMARY,
            FluentColors.TEXT_PRIMARY
        ]
        
        for color in colors:
            self.assertTrue(color.startswith("#"))
            self.assertEqual(len(color), 7)  # #RRGGBB格式

class TestFluentTheme(unittest.TestCase):
    """测试Fluent主题类"""
    
    def test_get_font(self):
        """测试字体获取"""
        font = FluentTheme.get_font("normal", "normal")
        self.assertIsInstance(font, ctk.CTkFont)
        self.assertEqual(font.cget("family"), "Segoe UI")
    
    def test_get_color(self):
        """测试颜色获取"""
        color = FluentTheme.get_color("accent_primary")
        self.assertEqual(color, "#0078D4")
        
        # 测试不存在的颜色
        default_color = FluentTheme.get_color("nonexistent")
        self.assertEqual(default_color, "#000000")
    
    def test_theme_config(self):
        """测试主题配置"""
        light_config = FluentTheme.get_theme_config("light")
        self.assertIsInstance(light_config, dict)
        self.assertIn("CTk", light_config)
        self.assertIn("CTkButton", light_config)

class TestGUIComponents(unittest.TestCase):
    """测试GUI组件"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试用的根窗口
        self.root = ctk.CTk()
        self.root.withdraw()  # 隐藏窗口
    
    def tearDown(self):
        """测试后清理"""
        if self.root:
            self.root.destroy()
    
    def test_progress_ring_creation(self):
        """测试进度环创建"""
        try:
            progress_ring = FluentProgressRing(self.root, size=40)
            self.assertIsNotNone(progress_ring)
            self.assertEqual(progress_ring.size, 40)
            self.assertFalse(progress_ring.is_running)
        except Exception as e:
            self.fail(f"进度环创建失败: {e}")
    
    def test_progress_ring_animation(self):
        """测试进度环动画"""
        progress_ring = FluentProgressRing(self.root, size=40)
        
        # 测试启动动画
        progress_ring.start()
        self.assertTrue(progress_ring.is_running)
        
        # 测试停止动画
        progress_ring.stop()
        self.assertFalse(progress_ring.is_running)

class TestGUIApplication(unittest.TestCase):
    """测试GUI应用程序"""
    
    def setUp(self):
        """测试前准备"""
        # 模拟GUI环境
        self.app = None
    
    def tearDown(self):
        """测试后清理"""
        if self.app and hasattr(self.app, 'root'):
            try:
                self.app.root.destroy()
            except:
                pass
    
    def test_app_initialization(self):
        """测试应用初始化"""
        try:
            # 创建应用实例但不运行主循环
            self.app = ProxyFetcherGUI()
            self.app.root.withdraw()  # 隐藏窗口
            
            # 验证基本属性
            self.assertIsNotNone(self.app.root)
            self.assertIsNotNone(self.app.api_url)
            self.assertIsNotNone(self.app.fetch_count)
            self.assertIsNotNone(self.app.thread_count)
            
        except Exception as e:
            self.fail(f"应用初始化失败: {e}")
    
    def test_variable_initialization(self):
        """测试变量初始化"""
        self.app = ProxyFetcherGUI()
        self.app.root.withdraw()
        
        # 检查默认值
        self.assertEqual(self.app.fetch_count.get(), 3)
        self.assertEqual(self.app.thread_count.get(), 3)
        self.assertEqual(self.app.interval.get(), 1)
        self.assertFalse(self.app.is_fetching.get())

def run_gui_test():
    """运行GUI测试（需要显示环境）"""
    print("🎯 启动GUI测试...")
    
    try:
        # 创建测试应用
        app = ProxyFetcherGUI()
        app.root.title("测试模式 - 代理节点获取器")
        
        # 设置测试数据
        app.fetch_count.set(1)
        app.thread_count.set(1)
        app.interval.set(1)
        
        # 显示测试信息
        app.update_log("🧪 测试模式已启动")
        app.update_log("✅ GUI组件加载完成")
        app.update_log("ℹ️ 可以手动测试各项功能")
        
        print("✅ GUI测试窗口已打开")
        print("请手动测试以下功能:")
        print("1. 页面切换 (导航栏)")
        print("2. 主题切换 (设置页面)")
        print("3. 参数配置 (主页)")
        print("4. 日志显示 (日志页面)")
        print("5. 关闭窗口退出测试")
        
        # 运行GUI
        app.run()
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")

def main():
    """主测试函数"""
    print("🧪 代理节点获取器 - 测试套件")
    print("=" * 50)
    
    # 检查测试环境
    print("🔍 检查测试环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print(f"❌ Python版本过低: {sys.version}")
        return
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查依赖
    try:
        import customtkinter
        import requests
        print("✅ 依赖包检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        return
    
    # 运行单元测试
    print("\n📋 运行单元测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_classes = [
        TestProxyFetcher,
        TestFluentColors,
        TestFluentTheme,
        TestGUIComponents,
        TestGUIApplication
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 显示结果
    print(f"\n📊 测试结果:")
    print(f"总计: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    # 询问是否运行GUI测试
    if result.testsRun > 0 and len(result.failures) == 0 and len(result.errors) == 0:
        print("\n✅ 所有单元测试通过！")
        
        choice = input("\n是否运行GUI测试? (y/N): ").lower()
        if choice in ['y', 'yes']:
            run_gui_test()
    else:
        print("\n❌ 存在测试失败，请修复后再运行GUI测试")

if __name__ == "__main__":
    main()
