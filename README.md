# 代理节点获取器 - Fluent Design Edition

一个基于 Windows 11 Fluent Design 设计规范的现代化代理节点获取工具，提供美观的图形界面和强大的功能。

## ✨ 主要特性

### 🎨 界面设计
- **Windows 11 Fluent Design** 完整实现
- **Segoe UI 字体** 系统，8px 圆角设计
- **WinUI 3 间距规则** (8px 增量)
- **浅色/深色主题** 自动切换
- **Mica/Acrylic 效果** 半透明背景
- **NavigationView 布局** 自适应折叠菜单

### 🚀 核心功能
- **多线程并发获取** 代理节点
- **自定义配置** 获取次数、线程数、间隔时间
- **YAML 格式保存** 兼容主流代理工具
- **实时日志显示** 详细执行状态
- **进度环动画** Fluent 风格加载指示器
- **设置持久化** 自动保存用户配置

### 🛠️ 技术特性
- **CustomTkinter** 现代GUI框架
- **线程安全** 消息队列机制
- **异常处理** 完善的错误处理
- **资源管理** 自动清理和释放

## 📋 系统要求

- **操作系统**: Windows 10/11 (推荐 Windows 11)
- **Python**: 3.8+ 
- **内存**: 最低 512MB RAM
- **存储**: 50MB 可用空间

## 🔧 安装说明

### 方式一：直接运行 (推荐)
```bash
# 1. 克隆或下载项目文件
git clone <repository-url>
cd proxy-fetcher

# 2. 运行启动脚本（自动安装依赖）
python run.py
```

### 方式二：手动安装
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动应用
python proxy_fetcher_gui.py
```

### 方式三：可执行文件
```bash
# 构建可执行文件
python build_config.py

# 运行生成的exe文件
./dist/ProxyFetcher.exe
```

## 📖 使用指南

### 基本操作
1. **启动应用** - 运行 `python run.py` 或双击exe文件
2. **配置参数** - 在主页设置获取次数、线程数等
3. **选择目录** - 点击"浏览"选择输出目录
4. **开始获取** - 点击"🚀 开始获取"按钮
5. **查看结果** - 在日志页面查看详细执行信息

### 高级功能
- **主题切换**: 设置页面 → 外观设置 → 主题模式
- **API配置**: 设置页面 → API配置 → 修改API地址
- **日志导出**: 日志页面 → 导出日志 → 保存为文本文件

## 🎯 界面预览

### 主页面
- 现代化卡片式布局
- Fluent Design 按钮样式
- 实时进度显示

### 设置页面  
- API地址配置
- 主题模式切换
- 输出目录选择

### 日志页面
- 详细执行日志
- 日志导出功能
- 实时状态更新

## 🔨 开发说明

### 项目结构
```
proxy-fetcher/
├── proxy_fetcher.py          # 核心获取逻辑
├── proxy_fetcher_gui.py      # GUI主程序
├── fluent_theme.py           # Fluent Design主题
├── run.py                    # 启动脚本
├── build_config.py           # 打包配置
├── requirements.txt          # 依赖列表
├── README.md                 # 说明文档
└── dist/                     # 构建输出目录
```

### 自定义开发
1. **修改主题**: 编辑 `fluent_theme.py` 中的颜色配置
2. **添加功能**: 在 `proxy_fetcher_gui.py` 中扩展界面
3. **调整逻辑**: 修改 `proxy_fetcher.py` 中的获取逻辑

### 打包部署
```bash
# 生成可执行文件
python build_config.py

# 创建安装包 (需要NSIS)
makensis installer.nsi

# 创建MSIX包 (需要Windows SDK)
makeappx pack /d dist /p ProxyFetcher.msix
```

## 🎨 设计规范

### 颜色方案
- **主色调**: #0078D4 (Windows 11 Accent Blue)
- **悬停色**: #106EBE 
- **背景色**: #F3F3F3 (浅色) / #202020 (深色)
- **文本色**: #323130 (浅色) / #FFFFFF (深色)

### 控件规范
- **按钮圆角**: 4px (主按钮) / 8px (次要按钮)
- **卡片圆角**: 12px
- **间距规则**: 8px 增量 (8, 16, 24, 32...)
- **字体大小**: 12px (正文) / 14px (按钮) / 16px+ (标题)

### 动画效果
- **悬停动画**: 300ms 缓入曲线
- **进度环**: 50ms 刷新间隔
- **页面切换**: 淡入淡出效果

## 🐛 故障排除

### 常见问题
1. **依赖安装失败**
   ```bash
   # 升级pip
   python -m pip install --upgrade pip
   # 重新安装
   pip install -r requirements.txt
   ```

2. **界面显示异常**
   - 检查系统DPI设置
   - 确保Windows版本支持
   - 尝试切换主题模式

3. **获取失败**
   - 检查网络连接
   - 验证API地址
   - 查看详细日志

### 性能优化
- 减少线程数量避免过载
- 增加间隔时间降低频率
- 定期清理日志文件

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

### 贡献指南
1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- **Issues**: [GitHub Issues](https://github.com/your-repo/issues)
- **Email**: <EMAIL>
- **QQ群**: 123456789

---

**享受现代化的代理获取体验！** 🚀
