#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows 11 Fluent Design System
基于Microsoft官方设计规范的完整设计系统
参考文档：
- https://learn.microsoft.com/zh-cn/windows/apps/design/signature-experiences/color
- https://learn.microsoft.com/zh-cn/windows/apps/design/style/acrylic
- https://learn.microsoft.com/zh-cn/windows/apps/design/layout/layouts-with-xaml
"""

import customtkinter as ctk
from typing import Dict, Tuple, Any

class FluentDesignSystem:
    """Windows 11 Fluent Design System 完整实现"""

    # === 官方颜色规范 ===
    # 基于Microsoft Learn文档的精确颜色值

    # 主题色系统 (SystemAccentColor)
    ACCENT_COLORS = {
        "primary": "#0078D4",           # SystemAccentColor
        "secondary": "#106EBE",         # SystemAccentColorDark1
        "tertiary": "#005A9E",          # SystemAccentColorDark2
        "quaternary": "#004578",        # SystemAccentColorDark3
        "light1": "#40E0FF",           # SystemAccentColorLight1
        "light2": "#62E5FF",           # SystemAccentColorLight2
        "light3": "#9BFAFF",           # SystemAccentColorLight3
    }

    # 中性颜色系统 - 浅色主题
    LIGHT_THEME = {
        # 背景层次
        "background_primary": "#F3F3F3",      # 主背景
        "background_secondary": "#FAFAFA",    # 次级背景
        "background_tertiary": "#FFFFFF",     # 卡片背景
        "background_quaternary": "#F9F9F9",   # 输入控件背景

        # 文本颜色
        "text_primary": "#323130",            # 主要文本
        "text_secondary": "#605E5C",          # 次要文本
        "text_tertiary": "#8A8886",           # 三级文本
        "text_disabled": "#C8C6C4",           # 禁用文本
        "text_on_accent": "#FFFFFF",          # 强调色上的文本

        # 边框和分割线
        "stroke_primary": "#E1DFDD",          # 主要边框
        "stroke_secondary": "#EDEBE9",        # 次要边框
        "stroke_focus": "#0078D4",            # 焦点边框

        # 表面颜色
        "surface_stroke": "#E1DFDD",          # 表面边框
        "card_background": "#FFFFFF",         # 卡片背景
        "card_stroke": "#E1DFDD",            # 卡片边框

        # 控件状态
        "control_fill_default": "#FFFFFF",    # 控件默认填充
        "control_fill_secondary": "#F9F9F9",  # 控件次要填充
        "control_fill_tertiary": "#F3F3F3",   # 控件三级填充
        "control_fill_disabled": "#F9F9F9",   # 控件禁用填充

        # 悬停和按下状态
        "subtle_fill_transparent": "transparent",
        "subtle_fill_secondary": "#F9F9F9",
        "subtle_fill_tertiary": "#F3F3F3",
        "subtle_fill_disabled": "#F9F9F9",
    }

    # 中性颜色系统 - 深色主题
    DARK_THEME = {
        # 背景层次
        "background_primary": "#202020",      # 主背景
        "background_secondary": "#2C2C2C",    # 次级背景
        "background_tertiary": "#383838",     # 卡片背景
        "background_quaternary": "#404040",   # 输入控件背景

        # 文本颜色
        "text_primary": "#FFFFFF",            # 主要文本
        "text_secondary": "#E1E1E1",          # 次要文本
        "text_tertiary": "#C8C8C8",           # 三级文本
        "text_disabled": "#6D6D6D",           # 禁用文本
        "text_on_accent": "#FFFFFF",          # 强调色上的文本

        # 边框和分割线
        "stroke_primary": "#484644",          # 主要边框
        "stroke_secondary": "#3B3A39",        # 次要边框
        "stroke_focus": "#0078D4",            # 焦点边框

        # 表面颜色
        "surface_stroke": "#484644",          # 表面边框
        "card_background": "#383838",         # 卡片背景
        "card_stroke": "#484644",            # 卡片边框

        # 控件状态
        "control_fill_default": "#383838",    # 控件默认填充
        "control_fill_secondary": "#2C2C2C",  # 控件次要填充
        "control_fill_tertiary": "#202020",   # 控件三级填充
        "control_fill_disabled": "#2C2C2C",   # 控件禁用填充

        # 悬停和按下状态
        "subtle_fill_transparent": "transparent",
        "subtle_fill_secondary": "#2C2C2C",
        "subtle_fill_tertiary": "#202020",
        "subtle_fill_disabled": "#2C2C2C",
    }

    # === 状态颜色 ===
    STATUS_COLORS = {
        "success": "#107C10",     # 成功状态
        "warning": "#FF8C00",     # 警告状态
        "error": "#D13438",       # 错误状态
        "info": "#0078D4",        # 信息状态
    }

    # === 字体系统 ===
    # 基于Segoe UI字体系统
    TYPOGRAPHY = {
        "font_family": "Segoe UI",
        "font_family_mono": "Consolas",

        # 字体大小层次
        "caption": 12,            # 说明文字
        "body": 14,              # 正文
        "body_strong": 14,       # 强调正文
        "body_large": 18,        # 大正文
        "subtitle": 20,          # 副标题
        "title": 28,             # 标题
        "title_large": 40,       # 大标题
        "display": 68,           # 展示文字

        # 字重
        "weight_normal": "normal",
        "weight_semibold": "bold",
        "weight_bold": "bold",
    }

    # === 间距系统 ===
    # 基于8px网格系统
    SPACING = {
        "xs": 4,      # 0.5 * 8
        "sm": 8,      # 1 * 8
        "md": 16,     # 2 * 8
        "lg": 24,     # 3 * 8
        "xl": 32,     # 4 * 8
        "xxl": 40,    # 5 * 8
        "xxxl": 48,   # 6 * 8
    }

    # === 圆角系统 ===
    CORNER_RADIUS = {
        "none": 0,
        "small": 4,      # 小圆角
        "medium": 8,     # 中等圆角
        "large": 12,     # 大圆角
        "xlarge": 16,    # 超大圆角
        "circular": 9999, # 圆形
    }

    # === 阴影系统 ===
    # 基于Fluent Design的层次阴影
    ELEVATION = {
        "level_1": "0 1px 3px rgba(0,0,0,0.12)",
        "level_2": "0 3px 6px rgba(0,0,0,0.16)",
        "level_3": "0 10px 20px rgba(0,0,0,0.19)",
        "level_4": "0 14px 28px rgba(0,0,0,0.25)",
        "level_5": "0 19px 38px rgba(0,0,0,0.30)",
    }

    # === 动画系统 ===
    MOTION = {
        "duration_fast": 150,      # 快速动画
        "duration_normal": 300,    # 标准动画
        "duration_slow": 500,      # 慢速动画

        "easing_standard": "ease",
        "easing_accelerate": "ease-in",
        "easing_decelerate": "ease-out",
    }

    @classmethod
    def get_theme_colors(cls, theme_mode: str = "light") -> Dict[str, str]:
        """获取主题颜色"""
        if theme_mode == "dark":
            return cls.DARK_THEME
        return cls.LIGHT_THEME

    @classmethod
    def get_accent_color(cls, variant: str = "primary") -> str:
        """获取强调色"""
        return cls.ACCENT_COLORS.get(variant, cls.ACCENT_COLORS["primary"])

    @classmethod
    def get_status_color(cls, status: str) -> str:
        """获取状态颜色"""
        return cls.STATUS_COLORS.get(status, cls.STATUS_COLORS["info"])

    @classmethod
    def get_font(cls, size_key: str = "body", weight: str = "normal") -> ctk.CTkFont:
        """获取字体"""
        size = cls.TYPOGRAPHY.get(size_key, cls.TYPOGRAPHY["body"])
        return ctk.CTkFont(
            family=cls.TYPOGRAPHY["font_family"],
            size=size,
            weight=weight
        )

    @classmethod
    def get_spacing(cls, size_key: str = "md") -> int:
        """获取间距"""
        return cls.SPACING.get(size_key, cls.SPACING["md"])

    @classmethod
    def get_corner_radius(cls, size_key: str = "medium") -> int:
        """获取圆角"""
        return cls.CORNER_RADIUS.get(size_key, cls.CORNER_RADIUS["medium"])


class FluentComponents:
    """Fluent Design 组件库"""

    @staticmethod
    def create_card(parent, corner_radius: str = "large", **kwargs) -> ctk.CTkFrame:
        """创建Fluent风格卡片"""
        theme_colors = FluentDesignSystem.get_theme_colors()

        return ctk.CTkFrame(
            parent,
            corner_radius=FluentDesignSystem.get_corner_radius(corner_radius),
            fg_color=theme_colors["card_background"],
            border_width=1,
            border_color=theme_colors["card_stroke"],
            **kwargs
        )

    @staticmethod
    def create_button(parent, text: str, style: str = "accent", **kwargs) -> ctk.CTkButton:
        """创建Fluent风格按钮"""
        theme_colors = FluentDesignSystem.get_theme_colors()

        if style == "accent":
            fg_color = FluentDesignSystem.get_accent_color("primary")
            hover_color = FluentDesignSystem.get_accent_color("secondary")
            text_color = theme_colors["text_on_accent"]
        elif style == "subtle":
            fg_color = theme_colors["subtle_fill_secondary"]
            hover_color = theme_colors["subtle_fill_tertiary"]
            text_color = theme_colors["text_primary"]
        else:  # standard
            fg_color = theme_colors["control_fill_default"]
            hover_color = theme_colors["control_fill_secondary"]
            text_color = theme_colors["text_primary"]

        # 设置默认字体，但允许通过kwargs覆盖
        button_kwargs = {
            "corner_radius": FluentDesignSystem.get_corner_radius("small"),
            "fg_color": fg_color,
            "hover_color": hover_color,
            "text_color": text_color,
            "font": FluentDesignSystem.get_font("body", "normal")
        }

        # 更新kwargs，允许覆盖默认值
        button_kwargs.update(kwargs)

        return ctk.CTkButton(
            parent,
            text=text,
            **button_kwargs
        )

    @staticmethod
    def create_entry(parent, placeholder: str = "", **kwargs) -> ctk.CTkEntry:
        """创建Fluent风格输入框"""
        theme_colors = FluentDesignSystem.get_theme_colors()

        return ctk.CTkEntry(
            parent,
            placeholder_text=placeholder,
            corner_radius=FluentDesignSystem.get_corner_radius("small"),
            fg_color=theme_colors["control_fill_default"],
            border_color=theme_colors["stroke_primary"],
            text_color=theme_colors["text_primary"],
            placeholder_text_color=theme_colors["text_tertiary"],
            font=FluentDesignSystem.get_font("body"),
            **kwargs
        )
