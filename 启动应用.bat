@echo off
chcp 65001 >nul
title 代理节点获取器 - Fluent Design Edition

echo.
echo ========================================
echo   代理节点获取器 - Fluent Design Edition
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检测通过

:: 检查依赖文件
if not exist "requirements.txt" (
    echo ❌ 缺少requirements.txt文件
    pause
    exit /b 1
)

if not exist "proxy_fetcher_gui.py" (
    echo ❌ 缺少proxy_fetcher_gui.py文件
    pause
    exit /b 1
)

echo ✅ 必要文件检查完成

:: 安装/更新依赖
echo.
echo 🔄 检查并安装依赖包...
python -m pip install -r requirements.txt --quiet

if errorlevel 1 (
    echo ❌ 依赖安装失败，请检查网络连接
    pause
    exit /b 1
)

echo ✅ 依赖包准备完成

:: 启动应用
echo.
echo 🚀 启动应用程序...
echo.
python proxy_fetcher_gui.py

:: 如果程序异常退出，显示错误信息
if errorlevel 1 (
    echo.
    echo ❌ 程序异常退出
    echo 请检查错误信息或联系开发者
    pause
)

echo.
echo 程序已退出，按任意键关闭窗口...
pause >nul
